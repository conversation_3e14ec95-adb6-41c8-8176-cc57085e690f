/**
 * Interview Invitation Service
 * 
 * Handles creation, management, and validation of interview invitations
 * Supports secure token generation and email/SMS notifications
 */

import { supabase } from '@/utils/supabaseClient';
import { v4 as uuidv4 } from 'uuid';
import CryptoJS from 'crypto-js';

/**
 * Generate secure invitation token
 */
export const generateInvitationToken = () => {
  const timestamp = Date.now();
  const randomId = uuidv4();
  const tokenData = `${timestamp}-${randomId}`;
  
  // Encrypt the token for additional security
  const secretKey = import.meta.env.VITE_INVITATION_SECRET || 'flyt-interview-secret';
  const encryptedToken = CryptoJS.AES.encrypt(tokenData, secretKey).toString();
  
  // Make it URL-safe
  return encryptedToken.replace(/[+/=]/g, (match) => {
    switch (match) {
      case '+': return '-';
      case '/': return '_';
      case '=': return '';
      default: return match;
    }
  });
};

/**
 * Decrypt and validate invitation token
 */
export const validateInvitationToken = (token) => {
  try {
    // Convert back from URL-safe format
    const base64Token = token.replace(/[-_]/g, (match) => {
      switch (match) {
        case '-': return '+';
        case '_': return '/';
        default: return match;
      }
    });
    
    // Add padding if needed
    const paddedToken = base64Token + '='.repeat((4 - base64Token.length % 4) % 4);
    
    const secretKey = import.meta.env.VITE_INVITATION_SECRET || 'flyt-interview-secret';
    const decryptedBytes = CryptoJS.AES.decrypt(paddedToken, secretKey);
    const decryptedData = decryptedBytes.toString(CryptoJS.enc.Utf8);
    
    if (!decryptedData) {
      throw new Error('Invalid token');
    }
    
    const [timestamp, randomId] = decryptedData.split('-');
    const tokenAge = Date.now() - parseInt(timestamp);
    
    // Token expires after 24 hours
    const TOKEN_EXPIRY = 24 * 60 * 60 * 1000;
    
    if (tokenAge > TOKEN_EXPIRY) {
      throw new Error('Token expired');
    }
    
    return {
      valid: true,
      timestamp: parseInt(timestamp),
      randomId,
      age: tokenAge,
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message,
    };
  }
};

/**
 * Create interview invitation
 */
export const createInterviewInvitation = async (invitationData) => {
  try {
    const {
      interviewId,
      invitedBy,
      invitedRole, // 'candidate', 'interviewer', 'company'
      recipientEmail,
      recipientName,
      scheduledDate,
      duration,
      message,
      interviewType = 'video', // 'video', 'phone', 'in-person'
    } = invitationData;

    // Generate secure invitation token
    const invitationToken = generateInvitationToken();
    
    // Create invitation record
    const { data: invitation, error: invitationError } = await supabase
      .from('interview_invitations')
      .insert({
        id: uuidv4(),
        interview_id: interviewId,
        invitation_token: invitationToken,
        invited_by: invitedBy,
        invited_role: invitedRole,
        recipient_email: recipientEmail,
        recipient_name: recipientName,
        scheduled_date: scheduledDate,
        duration_minutes: duration,
        message,
        interview_type: interviewType,
        status: 'sent',
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        created_at: new Date(),
      })
      .select()
      .single();

    if (invitationError) throw invitationError;

    // Update interview with invitation token
    const { error: updateError } = await supabase
      .from('interviews')
      .update({
        invitation_token: invitationToken,
        meeting_link: `${window.location.origin}/interview/${invitationToken}`,
        updated_at: new Date(),
      })
      .eq('id', interviewId);

    if (updateError) throw updateError;

    return {
      success: true,
      invitation,
      invitationLink: `${window.location.origin}/interview/${invitationToken}`,
    };
  } catch (error) {
    console.error('Error creating interview invitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get invitation by token
 */
export const getInvitationByToken = async (token) => {
  try {
    // Validate token first
    const tokenValidation = validateInvitationToken(token);
    if (!tokenValidation.valid) {
      throw new Error(tokenValidation.error);
    }

    const { data, error } = await supabase
      .from('interview_invitations')
      .select(`
        *,
        interviews:interview_id (
          *,
          jobs:job_id (
            id,
            title,
            company_id
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          ),
          candidate_profiles:candidate_id (
            full_name,
            profile_photo_url,
            current_job_title
          ),
          interviewer_profiles:interviewer_id (
            full_name,
            profile_photo_url,
            current_designation
          )
        )
      `)
      .eq('invitation_token', token)
      .eq('status', 'sent')
      .gte('expires_at', new Date().toISOString())
      .single();

    if (error) throw error;

    return {
      success: true,
      invitation: data,
    };
  } catch (error) {
    console.error('Error getting invitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Accept invitation
 */
export const acceptInvitation = async (token, userId) => {
  try {
    const { data, error } = await supabase
      .from('interview_invitations')
      .update({
        status: 'accepted',
        accepted_by: userId,
        accepted_at: new Date(),
      })
      .eq('invitation_token', token)
      .select()
      .single();

    if (error) throw error;

    // Update interview status
    await supabase
      .from('interviews')
      .update({
        status: 'scheduled',
        updated_at: new Date(),
      })
      .eq('id', data.interview_id);

    return {
      success: true,
      invitation: data,
    };
  } catch (error) {
    console.error('Error accepting invitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Decline invitation
 */
export const declineInvitation = async (token, userId, reason = '') => {
  try {
    const { data, error } = await supabase
      .from('interview_invitations')
      .update({
        status: 'declined',
        declined_by: userId,
        declined_at: new Date(),
        decline_reason: reason,
      })
      .eq('invitation_token', token)
      .select()
      .single();

    if (error) throw error;

    // Update interview status
    await supabase
      .from('interviews')
      .update({
        status: 'cancelled',
        updated_at: new Date(),
      })
      .eq('id', data.interview_id);

    return {
      success: true,
      invitation: data,
    };
  } catch (error) {
    console.error('Error declining invitation:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Send invitation email/SMS
 */
export const sendInvitationNotification = async (invitation, invitationLink) => {
  try {
    // This would integrate with your email/SMS service
    // For now, we'll just log the invitation details
    console.log('Sending invitation notification:', {
      to: invitation.recipient_email,
      subject: 'Interview Invitation - Flyt Platform',
      invitationLink,
      scheduledDate: invitation.scheduled_date,
    });

    // TODO: Integrate with email service (SendGrid, AWS SES, etc.)
    // TODO: Integrate with SMS service (Twilio, AWS SNS, etc.)

    return {
      success: true,
      message: 'Invitation sent successfully',
    };
  } catch (error) {
    console.error('Error sending invitation notification:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
