/**
 * Interviewer Service - OPTIMIZED VERSION
 *
 * This service handles all API calls related to interviewers.
 * Updated to work with the new optimized database structure.
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Get a complete interviewer profile by ID (uses optimized view)
 * @param {string} id - Interviewer ID
 * @returns {Promise<Object>} - Complete interviewer profile
 */
export const getInterviewerProfile = async (id) => {
  try {
    // Use the optimized view for complete profile data
    const { data, error } = await supabase
      .from('interviewer_profiles_complete')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching interviewer profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create or update an interviewer profile (optimized two-step process)
 * @param {Object} profileData - Complete interviewer profile data
 * @returns {Promise<Object>} - Result of the operation
 */
export const upsertInterviewerProfile = async (profileData) => {
  console.log('Upserting interviewer profile:', profileData);
  try {
    // Step 1: Upsert profiles table (email, phone, role)
    const { error: profileError } = await supabase.from('profiles').upsert({
      id: profileData.id,
      email: profileData.email,
      phone_number: profileData.phone_number,
      role: 'interviewer',
      username: profileData.username || profileData.full_name,
      updated_at: new Date(),
    });

    if (profileError) throw profileError;

    // Step 2: Upsert interviewer-specific data (excluding email/phone)
    const interviewerData = { ...profileData };
    delete interviewerData.email;
    delete interviewerData.phone_number;
    delete interviewerData.role;
    delete interviewerData.username;

    const { data, error } = await supabase
      .from('interviewer_profiles')
      .upsert(interviewerData)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error upserting interviewer profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update an interviewer profile (optimized two-step process)
 * @param {string} id - Interviewer ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object>} - Result of the operation
 */
export const updateInterviewerProfile = async (id, updates) => {
  try {
    // Step 1: Update profiles table if email/phone are included
    if (updates.email || updates.phone_number || updates.username) {
      const profileUpdates = {};
      if (updates.email) profileUpdates.email = updates.email;
      if (updates.phone_number) profileUpdates.phone_number = updates.phone_number;
      if (updates.username) profileUpdates.username = updates.username;
      profileUpdates.updated_at = new Date();

      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', id);

      if (profileError) throw profileError;
    }

    // Step 2: Update interviewer-specific data (excluding email/phone)
    const interviewerUpdates = { ...updates };
    delete interviewerUpdates.email;
    delete interviewerUpdates.phone_number;
    delete interviewerUpdates.username;
    delete interviewerUpdates.role;
    interviewerUpdates.updated_at = new Date();

    const { data, error } = await supabase
      .from('interviewer_profiles')
      .update(interviewerUpdates)
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating interviewer profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Upload an interviewer profile photo
 * @param {string} interviewerId - Interviewer ID
 * @param {File} file - Profile photo file
 * @returns {Promise<Object>} - Result of the operation
 */
export const uploadProfilePhoto = async (interviewerId, file) => {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${interviewerId}/profile-${Date.now()}.${fileExt}`;

    // Using the candidate_photos bucket since there's no specific interviewer photos bucket
    // We could create a new bucket for interviewers, but for now we'll use the existing one
    const { data, error } = await supabase.storage.from('candidate_photos').upload(fileName, file, {
      cacheControl: '3600',
      upsert: true,
    });

    if (error) throw error;

    // Get the public URL
    const { data: urlData } = supabase.storage.from('candidate_photos').getPublicUrl(fileName);

    // Update the interviewer profile with the photo URL
    await updateInterviewerProfile(interviewerId, { profile_photo_url: urlData.publicUrl });

    return { success: true, data: urlData.publicUrl };
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all interview requests for an interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Interview requests
 */
export const getInterviewRequests = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          mobile_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url
        ),
        jobs:job_id (
          id,
          title,
          company_id
        ),
        companies:jobs.company_id (
          id,
          company_name,
          company_logo_url
        )
      `
      )
      .eq('interviewer_id', interviewerId)
      .eq('status', 'requested');

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching interview requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all scheduled interviews for an interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Scheduled interviews
 */
export const getScheduledInterviews = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          mobile_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url
        ),
        jobs:job_id (
          id,
          title,
          company_id
        ),
        companies:jobs.company_id (
          id,
          company_name,
          company_logo_url
        )
      `
      )
      .eq('interviewer_id', interviewerId)
      .eq('status', 'scheduled');

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching scheduled interviews:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all completed interviews for an interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Completed interviews
 */
export const getCompletedInterviews = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          years_experience,
          current_job_title,
          current_company
        ),
        jobs:job_id (
          id,
          title,
          company_id
        ),
        companies:jobs.company_id (
          id,
          company_name
        )
      `
      )
      .eq('interviewer_id', interviewerId)
      .eq('status', 'completed');

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching completed interviews:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get earnings for an interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Earnings data
 */
export const getInterviewerEarnings = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select('*')
      .eq('interviewer_id', interviewerId)
      .eq('status', 'completed');

    if (error) throw error;

    // Calculate earnings (assuming a fixed rate per interview)
    const totalInterviews = data.length;
    const ratePerInterview = 500; // ₹500 per interview
    const totalEarnings = totalInterviews * ratePerInterview;

    return {
      success: true,
      data: {
        totalInterviews,
        totalEarnings,
        ratePerInterview,
        interviews: data,
      },
    };
  } catch (error) {
    console.error('Error fetching interviewer earnings:', error);
    return { success: false, error: error.message };
  }
};
