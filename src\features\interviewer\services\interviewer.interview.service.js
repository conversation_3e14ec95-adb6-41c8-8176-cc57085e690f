/**
 * Interviewer Interview Service
 * 
 * Handles interviewer-specific interview operations and API calls
 * Includes accepting requests, conducting interviews, and providing feedback
 */

import { supabase } from '@/utils/supabaseClient';
import { generateInterviewLink } from '@/services/interviewLink.service';
import cacheService from '@/utils/cacheService';

/**
 * Interviewer Interview Service for interview-related operations
 */
const interviewerInterviewService = {
  /**
   * Accept an interview request
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - ID of the interviewer accepting the request
   * @param {Object} scheduleData - Optional schedule data
   * @returns {Promise<Object>} - Result of the operation
   */
  async acceptInterviewRequest(interviewId, interviewerId, scheduleData = {}) {
    try {
      const updateData = {
        status: 'scheduled',
        interviewer_id: interviewerId,
        accepted_at: new Date().toISOString(),
      };

      // Add schedule data if provided
      if (scheduleData.interview_date) {
        updateData.interview_date = scheduleData.interview_date;
      }
      if (scheduleData.duration_minutes) {
        updateData.duration_minutes = scheduleData.duration_minutes;
      }
      if (scheduleData.meeting_link) {
        updateData.meeting_link = scheduleData.meeting_link;
      }

      const { data, error } = await supabase
        .from('interviews')
        .update(updateData)
        .eq('id', interviewId)
        .eq('status', 'requested') // Only accept if still in requested status
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (profile_photo_url, current_job_title)
          ),
          jobs:job_id (
            id,
            title,
            company_id
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          )
        `);

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview request not found or already accepted by another interviewer');
      }

      // Clear relevant caches
      this.clearInterviewCaches(interviewerId);

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error accepting interview request:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Decline an interview request
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for logging)
   * @param {string} reason - Decline reason (optional)
   * @returns {Promise<Object>} - Result of the operation
   */
  async declineInterviewRequest(interviewId, interviewerId, reason = '') {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .update({
          status: 'declined',
          decline_reason: reason,
          declined_at: new Date().toISOString(),
          declined_by: interviewerId,
        })
        .eq('id', interviewId)
        .eq('status', 'requested')
        .select();

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview request not found or already processed');
      }

      // Clear relevant caches
      this.clearInterviewCaches(interviewerId);

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error declining interview request:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Reschedule an interview
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for verification)
   * @param {Object} newSchedule - New schedule data
   * @returns {Promise<Object>} - Result of the operation
   */
  async rescheduleInterview(interviewId, interviewerId, newSchedule) {
    try {
      const updateData = {
        interview_date: newSchedule.interview_date,
        duration_minutes: newSchedule.duration_minutes || null,
        meeting_link: newSchedule.meeting_link || null,
        reschedule_reason: newSchedule.reason || null,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('interviews')
        .update(updateData)
        .eq('id', interviewId)
        .eq('interviewer_id', interviewerId) // Ensure interviewer can only reschedule their own interviews
        .eq('status', 'scheduled') // Only allow rescheduling of scheduled interviews
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (profile_photo_url)
          ),
          jobs:job_id (
            id,
            title
          )
        `);

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or cannot be rescheduled');
      }

      // Clear relevant caches
      this.clearInterviewCaches(interviewerId);

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error rescheduling interview:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Complete an interview and submit feedback
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for verification)
   * @param {Object} feedbackData - Feedback data (score, feedback, recommendations)
   * @returns {Promise<Object>} - Result of the operation
   */
  async completeInterview(interviewId, interviewerId, feedbackData) {
    try {
      const updateData = {
        status: 'completed',
        score: feedbackData.score,
        feedback: feedbackData.feedback,
        technical_skills_rating: feedbackData.technical_skills_rating || null,
        communication_rating: feedbackData.communication_rating || null,
        problem_solving_rating: feedbackData.problem_solving_rating || null,
        cultural_fit_rating: feedbackData.cultural_fit_rating || null,
        recommendation: feedbackData.recommendation || null,
        completed_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('interviews')
        .update(updateData)
        .eq('id', interviewId)
        .eq('interviewer_id', interviewerId) // Ensure interviewer can only complete their own interviews
        .in('status', ['scheduled', 'in_progress'])
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name)
          ),
          jobs:job_id (
            id,
            title
          )
        `);

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or cannot be completed');
      }

      const interview = data[0];

      // If there's a job application associated with this interview,
      // update the application status and score
      if (interview.job_id && interview.candidate_id) {
        const { data: applicationData, error: applicationError } = await supabase
          .from('applications')
          .select('*')
          .eq('candidate_id', interview.candidate_id)
          .eq('job_id', interview.job_id)
          .single();

        if (!applicationError && applicationData) {
          await supabase
            .from('applications')
            .update({
              status: 'interviewed',
              interview_score: feedbackData.score,
              updated_at: new Date().toISOString(),
            })
            .eq('id', applicationData.id);
        }
      }

      // Clear relevant caches
      this.clearInterviewCaches(interviewerId);

      return { success: true, data: interview };
    } catch (error) {
      console.error('Error completing interview:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Cancel an interview
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for verification)
   * @param {string} reason - Cancellation reason
   * @returns {Promise<Object>} - Result of the operation
   */
  async cancelInterview(interviewId, interviewerId, reason) {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .update({
          status: 'cancelled',
          cancellation_reason: reason,
          cancelled_at: new Date().toISOString(),
          cancelled_by: interviewerId,
        })
        .eq('id', interviewId)
        .eq('interviewer_id', interviewerId) // Ensure interviewer can only cancel their own interviews
        .eq('status', 'scheduled')
        .select();

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or cannot be cancelled');
      }

      // Clear relevant caches
      this.clearInterviewCaches(interviewerId);

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error cancelling interview:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Start an interview (mark as in progress)
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for verification)
   * @returns {Promise<Object>} - Result of the operation
   */
  async startInterview(interviewId, interviewerId) {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .update({
          status: 'in_progress',
          started_at: new Date().toISOString(),
        })
        .eq('id', interviewId)
        .eq('interviewer_id', interviewerId)
        .eq('status', 'scheduled')
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (
              profile_photo_url,
              current_job_title,
              years_experience,
              resume_url
            )
          ),
          jobs:job_id (
            id,
            title,
            description,
            required_skills
          )
        `);

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or cannot be started');
      }

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error starting interview:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Update interview notes during the interview
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for verification)
   * @param {string} notes - Interview notes
   * @returns {Promise<Object>} - Result of the operation
   */
  async updateInterviewNotes(interviewId, interviewerId, notes) {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .update({
          notes,
          updated_at: new Date().toISOString(),
        })
        .eq('id', interviewId)
        .eq('interviewer_id', interviewerId)
        .select('id, notes');

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or access denied');
      }

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error updating interview notes:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get interview details for conducting the interview
   * @param {string} interviewId - Interview ID
   * @param {string} interviewerId - Interviewer ID (for verification)
   * @returns {Promise<Object>} - Interview details
   */
  async getInterviewDetails(interviewId, interviewerId) {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name, mobile_number),
            candidate_profiles:id (
              profile_photo_url,
              current_job_title,
              current_company,
              years_experience,
              resume_url,
              skills,
              expected_ctc,
              notice_period
            )
          ),
          jobs:job_id (
            id,
            title,
            description,
            required_skills,
            experience_level,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('id', interviewId)
        .eq('interviewer_id', interviewerId)
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching interview details:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Clear interview-related caches for an interviewer
   * @param {string} interviewerId - Interviewer ID
   */
  clearInterviewCaches(interviewerId) {
    if (!interviewerId) return;
    
    // Clear interviewer-specific caches
    const cacheKeys = [
      'interviewer_interviews',
      'interviewer_requests',
      'interviewer_schedule',
      'interviewer_stats',
    ];
    
    cacheKeys.forEach(prefix => {
      const cacheKey = cacheService.getCacheKey(prefix, interviewerId);
      cacheService.del(cacheKey);
    });
  }
};

export default interviewerInterviewService;
