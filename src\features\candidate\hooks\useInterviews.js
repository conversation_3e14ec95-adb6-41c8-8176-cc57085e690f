/**
 * useInterviews Hook for Candidates
 *
 * <PERSON>les fetching and managing interview data for candidates.
 * Integrates with candidate store for state management.
 * Uses optimized caching for better performance.
 */

import { useCallback, useEffect } from 'react';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import dataFetchService from '@/services/dataFetchService';

const useInterviews = () => {
  const { user, profile } = useAuth();

  // Get interviews data and methods from store
  const {
    interviews,
    interviewRequests,
    upcomingInterviews,
    interviewsLoading: loading,
    interviewsError: error,
    fetchInterviews,
    createInterviewRequest,
    updateInterviewStatus,
  } = useCandidateStore();

  /**
   * Fetch all interviews for the candidate
   * Uses store method with caching
   */
  const fetchAllInterviews = useCallback(
    async (forceRefresh = false) => {
      if (!user?.id) return [];

      try {
        // Use store method to fetch interviews
        const data = await fetchInterviews(user.id);
        return data || [];
      } catch (error) {
        console.error('Error fetching candidate interviews:', error);
        return [];
      }
    },
    [user?.id, fetchInterviews]
  );

  /**
   * Get upcoming interviews (requested or scheduled)
   */
  const getUpcomingInterviews = useCallback(() => {
    return interviews.filter((interview) => ['requested', 'scheduled'].includes(interview.status));
  }, [interviews]);

  /**
   * Get past interviews (completed or cancelled)
   */
  const getPastInterviews = useCallback(() => {
    return interviews.filter((interview) => ['completed', 'cancelled'].includes(interview.status));
  }, [interviews]);

  /**
   * Get interviews by status
   */
  const getInterviewsByStatus = useCallback(
    (status) => {
      return interviews.filter((interview) => interview.status === status);
    },
    [interviews]
  );

  /**
   * Get interview statistics
   */
  const getInterviewStats = useCallback(() => {
    const stats = {
      total: interviews.length,
      upcoming: getUpcomingInterviews().length,
      past: getPastInterviews().length,
      requested: getInterviewsByStatus('requested').length,
      scheduled: getInterviewsByStatus('scheduled').length,
      completed: getInterviewsByStatus('completed').length,
      cancelled: getInterviewsByStatus('cancelled').length,
    };

    return stats;
  }, [interviews, getUpcomingInterviews, getPastInterviews, getInterviewsByStatus]);

  // Initial fetch on mount
  useEffect(() => {
    if (user?.id) {
      fetchAllInterviews();
    }
  }, [user?.id, fetchAllInterviews]);

  return {
    // Data - using store's separated data
    interviews,
    interviewRequests, // Candidate's pending requests
    upcomingInterviews, // Accepted/scheduled interviews
    pastInterviews: getPastInterviews(),

    // UI states
    loading,
    error,

    // Actions
    fetchInterviews: fetchAllInterviews,
    createRequest: createInterviewRequest,
    updateStatus: updateInterviewStatus,
    refetch: () => fetchAllInterviews(),

    // Utilities
    getInterviewsByStatus,
    getInterviewStats,

    // Force refresh
    forceRefresh: () => fetchAllInterviews(),
  };
};

export default useInterviews;
