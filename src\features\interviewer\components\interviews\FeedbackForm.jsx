import React from 'react';
import { Form, Input, Button, Rate, Card, Typography, Divider, Select } from 'antd';
import { SaveOutlined, SendOutlined, StarOutlined } from '@ant-design/icons';
import { FEEDBACK_CATEGORIES, SCORE_RANGES } from '@/features/interviewer/constants';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const FeedbackForm = ({
  onSubmit,
  initialValues = {},
  loading = false,
  interview = {},
  editMode = false,
}) => {
  const [form] = Form.useForm();

  const handleFinish = (values) => {
    onSubmit({
      ...values,
      interview_id: interview.id,
      candidate_id: interview.candidate_id,
      job_id: interview.job_id,
      submitted: values.submit,
    });
  };

  const getScoreDescription = (score) => {
    for (const [key, range] of Object.entries(SCORE_RANGES)) {
      if (score >= range.min && score <= range.max) {
        return range.label;
      }
    }
    return '';
  };

  return (
    <Card className="shadow-sm">
      <Title level={3}>{editMode ? 'Edit Interview Feedback' : 'Submit Interview Feedback'}</Title>

      <div className="mb-6">
        <Paragraph>
          <strong>Candidate:</strong> {interview.candidate_name}
        </Paragraph>
        <Paragraph>
          <strong>Position:</strong> {interview.job_title}
        </Paragraph>
        <Paragraph>
          <strong>Company:</strong> {interview.company_name}
        </Paragraph>
        <Paragraph>
          <strong>Interview Date:</strong> {interview.date} at {interview.time}
        </Paragraph>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          submit: true,
          ...initialValues,
        }}
        onFinish={handleFinish}
      >
        <Divider orientation="left">Candidate Assessment</Divider>

        {FEEDBACK_CATEGORIES.map((category) => (
          <Form.Item
            key={category}
            name={['scores', category.toLowerCase().replace(/\s+/g, '_')]}
            label={category}
            rules={[{ required: true, message: `Please rate ${category}` }]}
          >
            <Rate
              allowHalf
              character={<StarOutlined />}
              count={5}
              tooltips={['Poor', 'Below Average', 'Average', 'Good', 'Excellent']}
            />
          </Form.Item>
        ))}

        <Form.Item
          name="overall_score"
          label="Overall Score"
          rules={[{ required: true, message: 'Please provide an overall score' }]}
        >
          <Rate
            allowHalf
            character={<StarOutlined />}
            count={5}
            tooltips={['Poor', 'Below Average', 'Average', 'Good', 'Excellent']}
          />
          {form.getFieldValue('overall_score') && (
            <Text className="ml-2">
              {getScoreDescription(form.getFieldValue('overall_score') * 20)}
            </Text>
          )}
        </Form.Item>

        <Form.Item
          name="recommendation"
          label="Hiring Recommendation"
          rules={[{ required: true, message: 'Please select a recommendation' }]}
        >
          <Select placeholder="Select your recommendation">
            <Option value="strong_yes">Strong Yes - Exceptional candidate</Option>
            <Option value="yes">Yes - Good fit for the role</Option>
            <Option value="maybe">Maybe - Has potential but some concerns</Option>
            <Option value="no">No - Not a good fit for this role</Option>
            <Option value="strong_no">Strong No - Significant concerns</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="strengths"
          label="Candidate Strengths"
          rules={[{ required: true, message: 'Please list candidate strengths' }]}
        >
          <TextArea
            rows={4}
            placeholder="List the candidate's key strengths and positive attributes"
          />
        </Form.Item>

        <Form.Item
          name="areas_for_improvement"
          label="Areas for Improvement"
          rules={[{ required: true, message: 'Please list areas for improvement' }]}
        >
          <TextArea
            rows={4}
            placeholder="List areas where the candidate could improve"
          />
        </Form.Item>

        <Form.Item
          name="additional_notes"
          label="Additional Notes"
        >
          <TextArea
            rows={4}
            placeholder="Any other observations or comments"
          />
        </Form.Item>

        <Divider orientation="left">Submission Options</Divider>

        <Form.Item
          name="submit"
          valuePropName="checked"
        >
          <Select defaultValue={true}>
            <Option value={true}>Submit Final Feedback</Option>
            <Option value={false}>Save as Draft</Option>
          </Select>
          <Text
            type="secondary"
            className="block mt-1"
          >
            {form.getFieldValue('submit')
              ? 'Feedback will be submitted and visible to the company'
              : 'Feedback will be saved as draft and only visible to you'}
          </Text>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={form.getFieldValue('submit') ? <SendOutlined /> : <SaveOutlined />}
          >
            {form.getFieldValue('submit') ? 'Submit Feedback' : 'Save as Draft'}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default FeedbackForm;
