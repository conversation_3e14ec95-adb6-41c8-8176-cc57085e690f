/**
 * Interview<PERSON>xample Component
 * 
 * Example component showing how to integrate the video call interview system
 * This demonstrates the complete workflow from invitation creation to joining
 */

import { useState } from 'react';
import { Button, Card, Space, Typography, Alert, Steps } from 'antd';
import { VideoCameraOutlined, LinkOutlined, UserAddOutlined } from '@ant-design/icons';
import CreateInvitation from './CreateInvitation';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const InterviewExample = () => {
  const [showCreateInvitation, setShowCreateInvitation] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: 'Create Interview',
      description: 'Set up interview details and generate invitation',
      icon: <UserAddOutlined />,
    },
    {
      title: 'Send Invitation',
      description: 'Share secure link with participants',
      icon: <LinkOutlined />,
    },
    {
      title: 'Join Interview',
      description: 'Start video call when ready',
      icon: <VideoCameraOutlined />,
    },
  ];

  const handleCreateInvitation = () => {
    setShowCreateInvitation(true);
  };

  const handleInvitationSuccess = (result) => {
    setShowCreateInvitation(false);
    setCurrentStep(1);
    console.log('Invitation created:', result);
  };

  const simulateJoinInterview = () => {
    // In a real scenario, this would open the interview room
    const sampleToken = 'sample-encrypted-token-here';
    const interviewUrl = `/interview/${sampleToken}`;
    
    // For demo purposes, just show the URL
    alert(`Interview room URL: ${window.location.origin}${interviewUrl}`);
    setCurrentStep(2);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Title level={2}>Video Call Interview System Demo</Title>
      
      <Alert
        message="Complete Interview Workflow"
        description="This demo shows the complete process of creating, sending, and joining video call interviews."
        type="info"
        showIcon
        className="mb-6"
      />

      {/* Steps */}
      <Card className="mb-6">
        <Steps current={currentStep} className="mb-6">
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>
      </Card>

      {/* Step Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Create Invitation */}
        <Card
          title="1. Create Interview Invitation"
          extra={<UserAddOutlined />}
          className={currentStep >= 0 ? 'border-blue-200' : ''}
        >
          <Paragraph>
            Create a secure interview invitation with encrypted tokens that expire in 24 hours.
          </Paragraph>
          
          <Space direction="vertical" className="w-full">
            <Text strong>Features:</Text>
            <ul className="ml-4">
              <li>Secure token generation</li>
              <li>Email/SMS notifications</li>
              <li>Customizable interview details</li>
              <li>Role-based access control</li>
            </ul>
            
            <Button
              type="primary"
              icon={<LinkOutlined />}
              onClick={handleCreateInvitation}
              block
            >
              Create Invitation
            </Button>
          </Space>
        </Card>

        {/* Join Interview */}
        <Card
          title="2. Join Video Interview"
          extra={<VideoCameraOutlined />}
          className={currentStep >= 1 ? 'border-green-200' : ''}
        >
          <Paragraph>
            Join the interview room using the secure invitation link.
          </Paragraph>
          
          <Space direction="vertical" className="w-full">
            <Text strong>Features:</Text>
            <ul className="ml-4">
              <li>WebRTC video calling</li>
              <li>Screen sharing</li>
              <li>Real-time chat</li>
              <li>Call controls & statistics</li>
            </ul>
            
            <Button
              type="primary"
              icon={<VideoCameraOutlined />}
              onClick={simulateJoinInterview}
              disabled={currentStep < 1}
              block
            >
              {currentStep < 1 ? 'Create Invitation First' : 'Join Interview Room'}
            </Button>
          </Space>
        </Card>
      </div>

      {/* Technical Details */}
      <Card title="Technical Implementation" className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Title level={5}>Security</Title>
            <ul className="text-sm">
              <li>AES encrypted tokens</li>
              <li>24-hour expiration</li>
              <li>Row Level Security</li>
              <li>Role-based permissions</li>
            </ul>
          </div>
          
          <div>
            <Title level={5}>Video Technology</Title>
            <ul className="text-sm">
              <li>WebRTC peer-to-peer</li>
              <li>Screen sharing support</li>
              <li>Real-time messaging</li>
              <li>Connection monitoring</li>
            </ul>
          </div>
          
          <div>
            <Title level={5}>Integration</Title>
            <ul className="text-sm">
              <li>Global routing system</li>
              <li>All roles supported</li>
              <li>Mobile responsive</li>
              <li>Email/SMS ready</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Usage Examples */}
      <Card title="Usage Examples" className="mt-6">
        <Space direction="vertical" className="w-full">
          <div>
            <Text strong>Invitation URLs:</Text>
            <div className="bg-gray-100 p-3 rounded mt-2 font-mono text-sm">
              https://yourapp.com/interview/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
            </div>
          </div>
          
          <div>
            <Text strong>Supported Roles:</Text>
            <div className="flex gap-2 mt-2">
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">Candidate</span>
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Interviewer</span>
              <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">Company</span>
            </div>
          </div>
          
          <div>
            <Text strong>Interview Types:</Text>
            <div className="flex gap-2 mt-2">
              <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs">Video Call</span>
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">Phone Call</span>
              <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">In-Person</span>
            </div>
          </div>
        </Space>
      </Card>

      {/* Create Invitation Modal */}
      <CreateInvitation
        visible={showCreateInvitation}
        onCancel={() => setShowCreateInvitation(false)}
        onSuccess={handleInvitationSuccess}
        defaultData={{
          recipientName: 'John Doe',
          recipientEmail: '<EMAIL>',
          duration: 60,
          message: 'Looking forward to our interview discussion!',
        }}
      />
    </div>
  );
};

export default InterviewExample;
