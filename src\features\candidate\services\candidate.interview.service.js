/**
 * Candidate Interview Service
 * 
 * Handles candidate-specific interview operations and API calls
 * Includes interview requests, scheduling, and management
 */

import { supabase } from '@/utils/supabaseClient';
import { generateInterviewLink } from '@/services/interviewLink.service';
import cacheService, { CACHE_TTL, CACHE_KEYS } from '@/utils/cacheService';

/**
 * Candidate Interview Service for interview-related operations
 */
const candidateInterviewService = {
  /**
   * Create a new interview request
   * @param {Object} interviewData - Interview request data
   * @returns {Promise<Object>} - Result of the operation
   */
  async createInterviewRequest(interviewData) {
    try {
      // Set the status to 'requested'
      const data = {
        ...interviewData,
        status: 'requested',
        created_at: new Date().toISOString(),
      };

      const { data: result, error } = await supabase
        .from('interviews')
        .insert(data)
        .select(`
          *,
          jobs:job_id (
            id,
            title
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          )
        `);

      if (error) throw error;

      const interview = result[0];

      // Generate interview link for the request
      const linkResult = await generateInterviewLink({
        interview_id: interview.id,
        candidate_id: interview.candidate_id,
        interviewer_id: interview.interviewer_id,
        company_id: interview.company_id,
        job_id: interview.job_id,
        interview_date: interview.preferred_date || interview.interview_date,
        duration_minutes: interview.duration_minutes,
      });

      if (linkResult.success) {
        // Update interview with the generated link
        await supabase
          .from('interviews')
          .update({
            meeting_link: linkResult.data.interview_url,
            interview_link_id: linkResult.data.id,
          })
          .eq('id', interview.id);

        interview.meeting_link = linkResult.data.interview_url;
        interview.interview_link_id = linkResult.data.id;
      }

      // Clear candidate interview cache
      this.clearInterviewCache(interview.candidate_id);

      return { success: true, data: interview };
    } catch (error) {
      console.error('Error creating interview request:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get candidate's interview requests with caching
   * @param {string} candidateId - Candidate ID
   * @param {string} status - Filter by status (optional)
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Interview requests
   */
  async getCandidateInterviews(candidateId, status = null, forceRefresh = false) {
    if (!candidateId) return [];
    
    const cacheKey = cacheService.getCacheKey(
      CACHE_KEYS.INTERVIEWS, 
      candidateId, 
      status ? `status_${status}` : 'all'
    );
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedInterviews = cacheService.get(cacheKey);
      if (cachedInterviews) return cachedInterviews;
    }
    
    try {
      let query = supabase
        .from('interviews')
        .select(`
          *,
          interviewers:interviewer_id (
            id,
            full_name,
            current_designation,
            current_company,
            profile_photo_url
          ),
          jobs:job_id (
            id,
            title,
            experience_level,
            required_skills
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          )
        `)
        .eq('candidate_id', candidateId);

      // Filter by status if provided
      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching candidate interviews:', error);
      return [];
    }
  },

  /**
   * Get upcoming interviews for candidate
   * @param {string} candidateId - Candidate ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Upcoming interviews
   */
  async getUpcomingInterviews(candidateId, forceRefresh = false) {
    if (!candidateId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'upcoming');
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedInterviews = cacheService.get(cacheKey);
      if (cachedInterviews) return cachedInterviews;
    }
    
    try {
      // Get current date in ISO format
      const now = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          *,
          interviewers:interviewer_id (
            id,
            full_name,
            current_designation,
            current_company,
            profile_photo_url
          ),
          jobs:job_id (
            id,
            title,
            experience_level
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          )
        `)
        .eq('candidate_id', candidateId)
        .in('status', ['scheduled', 'confirmed'])
        .gte('interview_date', now)
        .order('interview_date', { ascending: true });

      if (error) throw error;

      // Cache the result with shorter TTL for time-sensitive data
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching upcoming interviews:', error);
      return [];
    }
  },

  /**
   * Get interview history for candidate
   * @param {string} candidateId - Candidate ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Interview history
   */
  async getInterviewHistory(candidateId, forceRefresh = false) {
    if (!candidateId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'history');
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedHistory = cacheService.get(cacheKey);
      if (cachedHistory) return cachedHistory;
    }
    
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          *,
          interviewers:interviewer_id (
            id,
            full_name,
            current_designation,
            current_company
          ),
          jobs:job_id (
            id,
            title,
            experience_level
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          )
        `)
        .eq('candidate_id', candidateId)
        .in('status', ['completed', 'cancelled', 'declined'])
        .order('interview_date', { ascending: false });

      if (error) throw error;

      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching interview history:', error);
      return [];
    }
  },

  /**
   * Cancel an interview request
   * @param {string} interviewId - Interview ID
   * @param {string} candidateId - Candidate ID (for verification)
   * @param {string} reason - Cancellation reason
   * @returns {Promise<Object>} - Result of the operation
   */
  async cancelInterview(interviewId, candidateId, reason = '') {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .update({
          status: 'cancelled',
          cancellation_reason: reason,
          cancelled_at: new Date().toISOString(),
        })
        .eq('id', interviewId)
        .eq('candidate_id', candidateId) // Ensure candidate can only cancel their own interviews
        .in('status', ['requested', 'scheduled']) // Only allow cancellation of pending interviews
        .select();

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or cannot be cancelled');
      }

      // Clear candidate interview cache
      this.clearInterviewCache(candidateId);

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error cancelling interview:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Reschedule an interview request
   * @param {string} interviewId - Interview ID
   * @param {string} candidateId - Candidate ID (for verification)
   * @param {Object} newSchedule - New schedule data
   * @returns {Promise<Object>} - Result of the operation
   */
  async rescheduleInterview(interviewId, candidateId, newSchedule) {
    try {
      const updateData = {
        preferred_date: newSchedule.preferred_date,
        duration_minutes: newSchedule.duration_minutes || null,
        interview_type: newSchedule.interview_type || null,
        message: newSchedule.message || null,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('interviews')
        .update(updateData)
        .eq('id', interviewId)
        .eq('candidate_id', candidateId) // Ensure candidate can only reschedule their own interviews
        .eq('status', 'requested') // Only allow rescheduling of pending requests
        .select();

      if (error) throw error;

      if (!data || data.length === 0) {
        throw new Error('Interview not found or cannot be rescheduled');
      }

      // Clear candidate interview cache
      this.clearInterviewCache(candidateId);

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error rescheduling interview:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get interview statistics for candidate
   * @param {string} candidateId - Candidate ID
   * @returns {Promise<Object>} - Interview statistics
   */
  async getInterviewStats(candidateId) {
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select('status, score')
        .eq('candidate_id', candidateId);

      if (error) throw error;

      const stats = {
        total: data.length,
        requested: data.filter(i => i.status === 'requested').length,
        scheduled: data.filter(i => i.status === 'scheduled').length,
        completed: data.filter(i => i.status === 'completed').length,
        cancelled: data.filter(i => i.status === 'cancelled').length,
        declined: data.filter(i => i.status === 'declined').length,
        averageScore: 0,
      };

      // Calculate average score for completed interviews
      const completedWithScores = data.filter(i => i.status === 'completed' && i.score);
      if (completedWithScores.length > 0) {
        const totalScore = completedWithScores.reduce((sum, i) => sum + i.score, 0);
        stats.averageScore = Math.round((totalScore / completedWithScores.length) * 10) / 10;
      }

      return { success: true, data: stats };
    } catch (error) {
      console.error('Error fetching interview stats:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Clear interview cache for a candidate
   * @param {string} candidateId - Candidate ID
   */
  clearInterviewCache(candidateId) {
    if (!candidateId) return;
    
    const cacheKeys = [
      cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'all'),
      cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'upcoming'),
      cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'history'),
      cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'status_requested'),
      cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, candidateId, 'status_scheduled'),
    ];
    
    cacheKeys.forEach(key => cacheService.del(key));
  }
};

export default candidateInterviewService;
