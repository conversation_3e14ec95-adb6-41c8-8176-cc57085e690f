/**
 * Candidate Store - STANDARDIZED VERSION
 *
 * Responsibilities:
 * - Job applications management
 * - Saved jobs tracking
 * - Assessment handling
 * - Interview scheduling
 * - Resume and photo uploads
 * - Candidate-specific workflows
 *
 * Follows standardized store patterns:
 * - Consistent naming conventions
 * - Standardized loading/error states
 * - Unified cache management
 * - Common action patterns
 *
 * Does NOT handle:
 * - Authentication (handled by auth store)
 * - Profile data (handled by auth store)
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { supabase } from '@/utils/supabaseClient';

// Constants for better maintainability
const STORAGE_BUCKETS = {
  PHOTOS: 'candidate_photos',
  RESUMES: 'candidate_resumes',
};

const CACHE_CONTROL = '3600';

// Initial state following standardized pattern
const initialCandidateState = {
  // === CORE DATA STATE ===
  // Job-related state
  appliedJobs: [],
  savedJobs: [],
  jobApplications: [],

  // Assessment-related state
  assessments: [],
  completedAssessments: [],

  // Interview-related state
  interviews: [],

  // === STANDARDIZED UI STATE ===
  // Main loading/error states
  loading: false,
  error: null,

  // Specific loading states for complex operations
  jobsLoading: false,
  assessmentsLoading: false,
  interviewsLoading: false,
  uploadsLoading: false,

  // Specific error states for better error handling
  jobsError: null,
  assessmentsError: null,
  interviewsError: null,
  uploadsError: null,

  // === STANDARDIZED CACHE MANAGEMENT ===
  _cache: {
    lastJobsFetch: null,
    lastAssessmentsFetch: null,
    lastInterviewsFetch: null,
    jobsExpiry: 5 * 60 * 1000, // 5 minutes
    assessmentsExpiry: 10 * 60 * 1000, // 10 minutes
    interviewsExpiry: 2 * 60 * 1000, // 2 minutes (more frequent updates)
    appliedJobsIds: new Set(),
    savedJobsIds: new Set(),
  },
};

const useCandidateStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...initialCandidateState,

        // === STANDARDIZED UI STATE HELPERS ===
        setLoading: (loading) => set({ loading }, false, 'candidate:setLoading'),
        setError: (error) => set({ error }, false, 'candidate:setError'),
        clearError: () => set({ error: null }, false, 'candidate:clearError'),

        setJobsLoading: (loading) =>
          set({ jobsLoading: loading }, false, 'candidate:setJobsLoading'),
        setJobsError: (error) => set({ jobsError: error }, false, 'candidate:setJobsError'),
        clearJobsError: () => set({ jobsError: null }, false, 'candidate:clearJobsError'),

        setAssessmentsLoading: (loading) =>
          set({ assessmentsLoading: loading }, false, 'candidate:setAssessmentsLoading'),
        setAssessmentsError: (error) =>
          set({ assessmentsError: error }, false, 'candidate:setAssessmentsError'),
        clearAssessmentsError: () =>
          set({ assessmentsError: null }, false, 'candidate:clearAssessmentsError'),

        setUploadsLoading: (loading) =>
          set({ uploadsLoading: loading }, false, 'candidate:setUploadsLoading'),
        setUploadsError: (error) =>
          set({ uploadsError: error }, false, 'candidate:setUploadsError'),
        clearUploadsError: () => set({ uploadsError: null }, false, 'candidate:clearUploadsError'),

        // === STANDARDIZED CACHE HELPERS ===
        updateJobsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastJobsFetch: Date.now() },
            },
            false,
            'candidate:updateJobsCache'
          );
        },

        isJobsCacheValid: () => {
          const { _cache } = get();
          return _cache.lastJobsFetch && Date.now() - _cache.lastJobsFetch < _cache.jobsExpiry;
        },

        updateAssessmentsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastAssessmentsFetch: Date.now() },
            },
            false,
            'candidate:updateAssessmentsCache'
          );
        },

        isAssessmentsCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastAssessmentsFetch &&
            Date.now() - _cache.lastAssessmentsFetch < _cache.assessmentsExpiry
          );
        },

        // === JOB-RELATED ACTIONS ===
        fetchAppliedJobs: async (userId, forceRefresh = false) => {
          // Check cache validity unless force refresh
          if (!forceRefresh && get().isJobsCacheValid()) {
            return get().appliedJobs;
          }

          get().setJobsLoading(true);
          get().clearJobsError();

          try {
            const { data, error } = await supabase
              .from('applications')
              .select(
                `
                *,
                jobs:job_id (
                  *,
                  companies:company_id (
                    id,
                    company_name,
                    company_logo_url,
                    company_type,
                    company_size
                  )
                )
              `
              )
              .eq('candidate_id', userId)
              .order('created_at', { ascending: false });

            if (error) throw error;

            set({ appliedJobs: data || [] }, false, 'candidate:fetchAppliedJobs:success');
            get().updateJobsCache();
            get().setJobsLoading(false);

            return data || [];
          } catch (error) {
            console.error('Error fetching applied jobs:', error);
            get().setJobsError(error.message);
            get().setJobsLoading(false);
            return [];
          }
        },

        fetchSavedJobs: async (userId) => {
          set({ jobsLoading: true, jobsError: null });

          try {
            const { data, error } = await supabase
              .from('saved_jobs')
              .select(
                `
          *,
          jobs:job_id (
            *,
            companies:company_id (
              id,
              company_name,
              company_logo_url,
              company_type,
              company_size
            )
          )
        `
              )
              .eq('candidate_id', userId)
              .order('created_at', { ascending: false });

            if (error) throw error;

            set({
              savedJobs: data || [],
              jobsLoading: false,
            });

            return data;
          } catch (error) {
            console.error('Error fetching saved jobs:', error);
            set({
              jobsError: error.message,
              jobsLoading: false,
            });
            return [];
          }
        },

        applyToJob: async (userId, jobId) => {
          set({ loading: true, error: null });

          try {
            const { data, error } = await supabase
              .from('applications')
              .insert({
                candidate_id: userId,
                job_id: jobId,
                status: 'applied',
                application_date: new Date().toISOString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })
              .select()
              .single();

            if (error) throw error;

            // Refresh applied jobs
            await get().fetchAppliedJobs(userId, true);

            set({ loading: false });
            return { success: true, data };
          } catch (error) {
            console.error('Error applying to job:', error);
            set({
              error: error.message,
              loading: false,
            });
            return { success: false, error: error.message };
          }
        },

        saveJob: async (userId, jobId) => {
          set({ loading: true, error: null });

          try {
            const { data, error } = await supabase
              .from('saved_jobs')
              .insert({
                candidate_id: userId,
                job_id: jobId,
                created_at: new Date(),
              })
              .select()
              .single();

            if (error) throw error;

            // Refresh saved jobs
            await get().fetchSavedJobs(userId);

            set({ loading: false });
            return { success: true, data };
          } catch (error) {
            console.error('Error saving job:', error);
            set({
              error: error.message,
              loading: false,
            });
            return { success: false, error: error.message };
          }
        },

        unsaveJob: async (userId, jobId) => {
          set({ loading: true, error: null });

          try {
            const { error } = await supabase
              .from('saved_jobs')
              .delete()
              .eq('candidate_id', userId)
              .eq('job_id', jobId);

            if (error) throw error;

            // Refresh saved jobs
            await get().fetchSavedJobs(userId);

            set({ loading: false });
            return { success: true };
          } catch (error) {
            console.error('Error unsaving job:', error);
            set({
              error: error.message,
              loading: false,
            });
            return { success: false, error: error.message };
          }
        },

        // Assessment-related actions
        fetchAssessments: async (userId) => {
          set({ assessmentsLoading: true, assessmentsError: null });

          try {
            const { data, error } = await supabase
              .from('candidate_assessments')
              .select('*')
              .eq('candidate_id', userId)
              .order('created_at', { ascending: false });

            if (error) throw error;

            const available = data?.filter((a) => a.status === 'pending') || [];
            const completed = data?.filter((a) => a.status === 'completed') || [];

            set({
              assessments: available,
              completedAssessments: completed,
              assessmentsLoading: false,
            });

            return { available, completed };
          } catch (error) {
            console.error('Error fetching assessments:', error);
            set({
              assessmentsError: error.message,
              assessmentsLoading: false,
            });
            return { available: [], completed: [] };
          }
        },

        // Interview-related actions
        fetchInterviews: async (userId) => {
          set({ interviewsLoading: true, interviewsError: null });

          try {
            const { data, error } = await supabase
              .from('interviews')
              .select(
                `
          *,
          jobs:job_id (
            title,
            companies:company_id (
              company_name
            )
          )
        `
              )
              .eq('candidate_id', userId)
              .order('scheduled_at', { ascending: true });

            if (error) throw error;

            set({
              interviews: data || [],
              interviewsLoading: false,
            });

            return data;
          } catch (error) {
            console.error('Error fetching interviews:', error);
            set({
              interviewsError: error.message,
              interviewsLoading: false,
            });
            return [];
          }
        },

        // File upload actions (profile photo, resume)
        uploadProfilePhoto: async (userId, file) => {
          set({ loading: true, error: null });

          try {
            const fileExt = file.name.split('.').pop();
            const fileName = `${userId}/profile-photo.${fileExt}`;

            const { error: uploadError } = await supabase.storage
              .from(STORAGE_BUCKETS.PHOTOS)
              .upload(fileName, file, {
                cacheControl: CACHE_CONTROL,
                upsert: true,
              });

            if (uploadError) throw uploadError;

            const { data } = supabase.storage.from(STORAGE_BUCKETS.PHOTOS).getPublicUrl(fileName);
            const publicUrl = data.publicUrl;

            // Update profile photo URL in candidate_profiles table
            const { error: updateError } = await supabase
              .from('candidate_profiles')
              .update({
                profile_photo_url: publicUrl,
                updated_at: new Date(),
              })
              .eq('id', userId);

            if (updateError) throw updateError;

            set({ loading: false });
            return { success: true, url: publicUrl };
          } catch (error) {
            console.error('Error uploading profile photo:', error);
            set({
              error: error.message,
              loading: false,
            });
            return { success: false, error: error.message };
          }
        },

        uploadResume: async (userId, file) => {
          set({ loading: true, error: null });

          try {
            const fileExt = file.name.split('.').pop();
            const fileName = `${userId}/resume.${fileExt}`;

            const { error: uploadError } = await supabase.storage
              .from(STORAGE_BUCKETS.RESUMES)
              .upload(fileName, file, {
                cacheControl: CACHE_CONTROL,
                upsert: true,
              });

            if (uploadError) throw uploadError;

            const { data } = supabase.storage.from(STORAGE_BUCKETS.RESUMES).getPublicUrl(fileName);
            const publicUrl = data.publicUrl;

            // Update resume URL in candidate_profiles table
            const { error: updateError } = await supabase
              .from('candidate_profiles')
              .update({
                resume_url: publicUrl,
                updated_at: new Date(),
              })
              .eq('id', userId);

            if (updateError) throw updateError;

            set({ loading: false });
            return { success: true, url: publicUrl };
          } catch (error) {
            console.error('Error uploading resume:', error);
            set({
              error: error.message,
              loading: false,
            });
            return { success: false, error: error.message };
          }
        },

        // === STANDARDIZED RESET METHOD ===
        resetStore: () => {
          set(initialCandidateState, false, 'candidate:resetStore');
        },

        // Legacy method for backward compatibility
        clearStore: () => {
          get().resetStore();
        },
      }),
      {
        name: 'candidate-storage',
        partialize: (state) => ({
          appliedJobs: state.appliedJobs,
          savedJobs: state.savedJobs,
          assessments: state.assessments,
          completedAssessments: state.completedAssessments,
          interviews: state.interviews,
          _cache: state._cache,
        }),
      }
    ),
    { name: 'candidate-store' }
  )
);

export default useCandidateStore;
