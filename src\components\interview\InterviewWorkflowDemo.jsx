/**
 * InterviewWorkflowDemo Component
 * 
 * Demonstration component showing the complete interview request workflow
 * From candidate request creation to interviewer acceptance and calendar integration
 */

import { useState } from 'react';
import { Card, Steps, Button, Typography, Space, Alert, Timeline, Tag } from 'antd';
import {
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const InterviewWorkflowDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const workflowSteps = [
    {
      title: 'Candidate Creates Request',
      description: 'Candidate selects job and preferred interview time',
      icon: <UserOutlined />,
      status: 'process',
    },
    {
      title: 'Request Visible to Interviewers',
      description: 'All interviewers can see the pending request',
      icon: <ClockCircleOutlined />,
      status: 'wait',
    },
    {
      title: 'Interviewer Accepts',
      description: 'Interviewer accepts and confirms schedule',
      icon: <CheckCircleOutlined />,
      status: 'wait',
    },
    {
      title: 'Calendar Integration',
      description: 'Interview appears on both calendars',
      icon: <CalendarOutlined />,
      status: 'wait',
    },
    {
      title: 'Interview Conducted',
      description: 'Video call interview takes place',
      icon: <VideoCameraOutlined />,
      status: 'wait',
    },
  ];

  const nextStep = () => {
    if (currentStep < workflowSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const resetDemo = () => {
    setCurrentStep(0);
  };

  const getStepStatus = (index) => {
    if (index < currentStep) return 'finish';
    if (index === currentStep) return 'process';
    return 'wait';
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <Title level={2}>Interview Request System - Workflow Demo</Title>
      
      <Alert
        message="Complete Interview Scheduling Workflow"
        description="This demo shows how the interview request system works from candidate request to final interview."
        type="info"
        showIcon
        className="mb-6"
      />

      {/* Workflow Steps */}
      <Card className="mb-6">
        <Steps
          current={currentStep}
          className="mb-6"
        >
          {workflowSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
              status={getStepStatus(index)}
            />
          ))}
        </Steps>

        <div className="text-center">
          <Space>
            <Button 
              type="primary" 
              onClick={nextStep}
              disabled={currentStep >= workflowSteps.length - 1}
            >
              Next Step
            </Button>
            <Button onClick={resetDemo}>
              Reset Demo
            </Button>
          </Space>
        </div>
      </Card>

      {/* Step Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Step Details */}
        <Card title={`Step ${currentStep + 1}: ${workflowSteps[currentStep]?.title}`}>
          {currentStep === 0 && (
            <div>
              <Paragraph>
                <strong>Candidate Action:</strong> Creates an interview request
              </Paragraph>
              <ul className="ml-4">
                <li>Selects job position from applied/available jobs</li>
                <li>Chooses preferred date and time</li>
                <li>Sets interview duration and type</li>
                <li>Adds personal message for interviewer</li>
                <li>Submits request with status "requested"</li>
              </ul>
              <Tag color="blue">Status: REQUESTED</Tag>
            </div>
          )}

          {currentStep === 1 && (
            <div>
              <Paragraph>
                <strong>System Behavior:</strong> Request becomes visible to all interviewers
              </Paragraph>
              <ul className="ml-4">
                <li>Request appears in all interviewers' "Requests" tab</li>
                <li>Shows candidate profile and job details</li>
                <li>Displays preferred time and message</li>
                <li>Provides Accept/Decline action buttons</li>
                <li>Real-time updates for all interviewers</li>
              </ul>
              <Tag color="orange">Status: PENDING</Tag>
            </div>
          )}

          {currentStep === 2 && (
            <div>
              <Paragraph>
                <strong>Interviewer Action:</strong> Accepts the interview request
              </Paragraph>
              <ul className="ml-4">
                <li>Reviews candidate profile and preferences</li>
                <li>Confirms or modifies interview schedule</li>
                <li>Adds meeting link (optional)</li>
                <li>Accepts request - becomes unavailable to others</li>
                <li>Request moves to interviewer's "Upcoming" tab</li>
              </ul>
              <Tag color="green">Status: SCHEDULED</Tag>
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <Paragraph>
                <strong>Calendar Integration:</strong> Automatic event creation
              </Paragraph>
              <ul className="ml-4">
                <li>Interview appears on both candidate and interviewer calendars</li>
                <li>Calendar event includes all interview details</li>
                <li>Meeting link and participant information included</li>
                <li>Export options: Google Calendar, Outlook, ICS file</li>
                <li>Real-time synchronization with status changes</li>
              </ul>
              <Tag color="purple">Status: CALENDAR_SYNCED</Tag>
            </div>
          )}

          {currentStep === 4 && (
            <div>
              <Paragraph>
                <strong>Interview Execution:</strong> Video call interview
              </Paragraph>
              <ul className="ml-4">
                <li>Both parties join using meeting link or invitation</li>
                <li>Video call with screen sharing and chat</li>
                <li>Interview controls and recording options</li>
                <li>Post-interview feedback collection</li>
                <li>Status updates to "completed"</li>
              </ul>
              <Tag color="green">Status: COMPLETED</Tag>
            </div>
          )}
        </Card>

        {/* System Features */}
        <Card title="System Features">
          <Timeline>
            <Timeline.Item 
              color={currentStep >= 0 ? 'green' : 'gray'}
              dot={<UserOutlined />}
            >
              <strong>Candidate Request Creation</strong>
              <br />
              Intuitive form with job selection and scheduling
            </Timeline.Item>
            
            <Timeline.Item 
              color={currentStep >= 1 ? 'blue' : 'gray'}
              dot={<ClockCircleOutlined />}
            >
              <strong>Global Request Visibility</strong>
              <br />
              All interviewers see pending requests in real-time
            </Timeline.Item>
            
            <Timeline.Item 
              color={currentStep >= 2 ? 'green' : 'gray'}
              dot={<CheckCircleOutlined />}
            >
              <strong>Conflict Prevention</strong>
              <br />
              Only one interviewer can accept each request
            </Timeline.Item>
            
            <Timeline.Item 
              color={currentStep >= 3 ? 'purple' : 'gray'}
              dot={<CalendarOutlined />}
            >
              <strong>Calendar Integration</strong>
              <br />
              Automatic event creation and export options
            </Timeline.Item>
            
            <Timeline.Item 
              color={currentStep >= 4 ? 'green' : 'gray'}
              dot={<VideoCameraOutlined />}
            >
              <strong>Video Interview</strong>
              <br />
              Complete video calling solution with controls
            </Timeline.Item>
          </Timeline>
        </Card>
      </div>

      {/* Technical Details */}
      <Card title="Technical Implementation" className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Title level={5}>Database</Title>
            <ul className="text-sm">
              <li>Enhanced interviews table</li>
              <li>Row Level Security policies</li>
              <li>Conflict prevention triggers</li>
              <li>Audit trail tracking</li>
            </ul>
          </div>
          
          <div>
            <Title level={5}>Real-time Updates</Title>
            <ul className="text-sm">
              <li>Zustand state management</li>
              <li>Automatic UI synchronization</li>
              <li>Optimistic updates</li>
              <li>Error recovery</li>
            </ul>
          </div>
          
          <div>
            <Title level={5}>Security</Title>
            <ul className="text-sm">
              <li>Role-based permissions</li>
              <li>Data validation</li>
              <li>Atomic operations</li>
              <li>Secure token handling</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Benefits */}
      <Card title="Key Benefits" className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Title level={5}>For Candidates</Title>
            <ul>
              <li>Proactive interview scheduling</li>
              <li>Flexible time preferences</li>
              <li>Clear status tracking</li>
              <li>Multiple position requests</li>
            </ul>
          </div>
          
          <div>
            <Title level={5}>For Interviewers</Title>
            <ul>
              <li>Centralized request management</li>
              <li>Flexible acceptance workflow</li>
              <li>Automatic calendar integration</li>
              <li>Efficient candidate selection</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default InterviewWorkflowDemo;
