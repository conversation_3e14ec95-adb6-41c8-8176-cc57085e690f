/**
 * Interview Link Service
 * 
 * Handles generation and management of interview links for the platform
 * Creates unique interview rooms that both candidates and interviewers can join
 */

import { supabase } from '@/utils/supabaseClient';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a unique interview link
 * @param {Object} interviewData - Interview data
 * @returns {Promise<Object>} - Generated link data
 */
export const generateInterviewLink = async (interviewData) => {
  try {
    // Generate unique interview room ID
    const roomId = uuidv4();
    const linkId = `interview_${roomId.replace(/-/g, '').substring(0, 12)}`;
    
    // Create interview link data
    const linkData = {
      id: linkId,
      interview_id: interviewData.interview_id,
      room_id: roomId,
      candidate_id: interviewData.candidate_id,
      interviewer_id: interviewData.interviewer_id,
      company_id: interviewData.company_id,
      job_id: interviewData.job_id,
      interview_date: interviewData.interview_date,
      duration_minutes: interviewData.duration_minutes || 60,
      status: 'active',
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    };

    // Store in database
    const { data, error } = await supabase
      .from('interview_links')
      .insert(linkData)
      .select()
      .single();

    if (error) throw error;

    // Generate the actual URL
    const baseUrl = window.location.origin;
    const interviewUrl = `${baseUrl}/interview-room/${linkId}`;

    return {
      success: true,
      data: {
        ...data,
        interview_url: interviewUrl,
        join_url: interviewUrl,
      }
    };
  } catch (error) {
    console.error('Error generating interview link:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview link details
 * @param {string} linkId - Interview link ID
 * @returns {Promise<Object>} - Interview link data
 */
export const getInterviewLink = async (linkId) => {
  try {
    const { data, error } = await supabase
      .from('interview_links')
      .select(`
        *,
        interviews:interview_id (
          id,
          status,
          interview_date,
          duration_minutes,
          interview_type
        ),
        candidate_profiles:candidate_id (
          id,
          full_name,
          profile_photo_url
        ),
        interviewer_profiles:interviewer_id (
          id,
          full_name,
          profile_photo_url
        ),
        company_profiles:company_id (
          company_name,
          company_logo_url
        ),
        jobs:job_id (
          title
        )
      `)
      .eq('id', linkId)
      .single();

    if (error) throw error;

    // Check if link is expired
    const now = new Date();
    const expiresAt = new Date(data.expires_at);
    
    if (now > expiresAt) {
      return { success: false, error: 'Interview link has expired' };
    }

    // Check if interview is still valid
    if (data.interviews?.status === 'cancelled' || data.interviews?.status === 'completed') {
      return { success: false, error: 'Interview is no longer active' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error getting interview link:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update interview link status
 * @param {string} linkId - Interview link ID
 * @param {string} status - New status
 * @returns {Promise<Object>} - Result
 */
export const updateInterviewLinkStatus = async (linkId, status) => {
  try {
    const { data, error } = await supabase
      .from('interview_links')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', linkId)
      .select()
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating interview link status:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Join interview room
 * @param {string} linkId - Interview link ID
 * @param {string} userId - User ID joining
 * @param {string} userRole - User role (candidate/interviewer)
 * @returns {Promise<Object>} - Join result
 */
export const joinInterviewRoom = async (linkId, userId, userRole) => {
  try {
    // Get interview link details
    const linkResult = await getInterviewLink(linkId);
    if (!linkResult.success) {
      return linkResult;
    }

    const linkData = linkResult.data;

    // Verify user is authorized to join
    const isAuthorized = 
      (userRole === 'candidate' && linkData.candidate_id === userId) ||
      (userRole === 'interviewer' && linkData.interviewer_id === userId);

    if (!isAuthorized) {
      return { success: false, error: 'You are not authorized to join this interview' };
    }

    // Check if interview time is appropriate (within 15 minutes of scheduled time)
    const interviewDate = new Date(linkData.interviews.interview_date);
    const now = new Date();
    const timeDiff = Math.abs(now - interviewDate) / (1000 * 60); // minutes

    if (timeDiff > 15 && now < interviewDate) {
      return { 
        success: false, 
        error: 'Interview room will be available 15 minutes before the scheduled time' 
      };
    }

    // Log the join attempt
    await supabase
      .from('interview_participants')
      .upsert({
        interview_link_id: linkId,
        user_id: userId,
        user_role: userRole,
        joined_at: new Date().toISOString(),
        status: 'joined'
      });

    return { 
      success: true, 
      data: {
        room_id: linkData.room_id,
        interview_data: linkData,
        can_join: true
      }
    };
  } catch (error) {
    console.error('Error joining interview room:', error);
    return { success: false, error: error.message };
  }
};

/**
 * End interview session
 * @param {string} linkId - Interview link ID
 * @param {string} userId - User ID ending the session
 * @returns {Promise<Object>} - Result
 */
export const endInterviewSession = async (linkId, userId) => {
  try {
    // Update participant status
    await supabase
      .from('interview_participants')
      .update({
        left_at: new Date().toISOString(),
        status: 'left'
      })
      .eq('interview_link_id', linkId)
      .eq('user_id', userId);

    return { success: true };
  } catch (error) {
    console.error('Error ending interview session:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview room participants
 * @param {string} linkId - Interview link ID
 * @returns {Promise<Object>} - Participants data
 */
export const getInterviewParticipants = async (linkId) => {
  try {
    const { data, error } = await supabase
      .from('interview_participants')
      .select(`
        *,
        candidate_profiles:user_id (
          full_name,
          profile_photo_url
        ),
        interviewer_profiles:user_id (
          full_name,
          profile_photo_url
        )
      `)
      .eq('interview_link_id', linkId)
      .eq('status', 'joined');

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error getting interview participants:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Validate interview link access
 * @param {string} linkId - Interview link ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Validation result
 */
export const validateInterviewAccess = async (linkId, userId) => {
  try {
    const linkResult = await getInterviewLink(linkId);
    if (!linkResult.success) {
      return linkResult;
    }

    const linkData = linkResult.data;
    
    // Check if user is participant
    const isCandidate = linkData.candidate_id === userId;
    const isInterviewer = linkData.interviewer_id === userId;
    
    if (!isCandidate && !isInterviewer) {
      return { success: false, error: 'Access denied' };
    }

    return { 
      success: true, 
      data: {
        ...linkData,
        user_role: isCandidate ? 'candidate' : 'interviewer',
        can_access: true
      }
    };
  } catch (error) {
    console.error('Error validating interview access:', error);
    return { success: false, error: error.message };
  }
};
