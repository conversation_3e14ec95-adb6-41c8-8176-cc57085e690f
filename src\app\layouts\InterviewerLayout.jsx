import React, { useState, useEffect } from 'react';
import { Layout, Switch, Spin } from 'antd';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  TeamOutlined,
  FileTextOutlined,
  DollarOutlined,
  VideoCameraOutlined,
  CalendarOutlined,
  UserOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  LogoutOutlined,
  BulbOutlined,
  BulbFilled,
} from '@ant-design/icons';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useColorModeStore } from '@/store/colorMode.store';
import useAuth from '@/hooks/useAuth';
import useLogout from '@/hooks/useLogout';
import useInterviewerStore from '@/features/interviewer/store/interviewer.store';
import { logo_lite, logo_dark } from '@/assets';
import SearchModal from '@/components/shared/SearchModal';
import AppSidebar from '@/components/layouts/AppSidebar';
import AppHeader from '@/components/layouts/AppHeader';
import MobileDrawer from '@/components/layouts/MobileDrawer';
import showToast from '@/utils/toast';
import PageTitle from '@/components/PageTitle';

const { Content, Footer } = Layout;

// Icon style for consistent sizing
const iconStyle = { fontSize: '18px' };

const InterviewerLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [upcomingInterviews, setUpcomingInterviews] = useState([]);

  const location = useLocation();
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(max-width: 991px)');
  const { colorMode, toggleColorMode } = useColorModeStore();
  const { profile } = useAuth();

  // Logout functionality with loading state
  const { handleLogout, isLoggingOut } = useLogout();
  const isDark = colorMode === 'dark';

  // Get interviewer data from store
  const { interviewRequests, scheduledInterviews } = useInterviewerStore();

  // Mock notifications data
  useEffect(() => {
    setNotifications([
      { id: 1, title: 'New interview request', read: false },
      { id: 2, title: 'Candidate feedback required', read: false },
      { id: 3, title: 'Interview rescheduled', read: true },
      { id: 4, title: 'Payment processed', read: true },
    ]);

    setUpcomingInterviews([
      { id: 1, candidate: 'John Doe', time: '10:00 AM', date: 'Today' },
      { id: 2, candidate: 'Jane Smith', time: '2:30 PM', date: 'Tomorrow' },
    ]);
  }, []);

  // Collapse sidebar on mobile by default
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    }
  }, [isMobile]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileDrawer = () => {
    setMobileDrawerOpen(!mobileDrawerOpen);
  };

  const handleThemeToggle = () => {
    toggleColorMode();
    showToast.success(`Switched to ${isDark ? 'light' : 'dark'} mode`);
  };

  const notificationItems = notifications.map((notification) => ({
    key: notification.id,
    label: (
      <div
        className={`notification-item ${!notification.read ? 'font-bold' : ''} p-2`}
        style={{ fontSize: '16px' }}
      >
        {notification.title}
      </div>
    ),
  }));

  const interviewItems = upcomingInterviews.map((interview) => ({
    key: interview.id,
    label: (
      <div className="p-2">
        <div className="font-semibold">{interview.candidate}</div>
        <div>
          {interview.date} at {interview.time}
        </div>
      </div>
    ),
  }));

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined style={iconStyle} />,
      label: (
        <Link
          to="/sourcer/profile"
          style={{ fontSize: '16px' }}
        >
          Profile
        </Link>
      ),
    },
    {
      key: 'setting',
      icon: <SettingOutlined style={iconStyle} />,
      label: (
        <Link
          to="/sourcer/setting"
          style={{ fontSize: '16px' }}
        >
          Setting
        </Link>
      ),
    },
    {
      key: 'theme',
      icon: isDark ? <BulbFilled style={iconStyle} /> : <BulbOutlined style={iconStyle} />,
      label: (
        <div className="flex items-center justify-between">
          <span style={{ fontSize: '16px', marginRight: '6px' }}>Dark Mode</span>
          <Switch
            checked={isDark}
            onChange={handleThemeToggle}
            size="default"
          />
        </div>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined style={iconStyle} />,
      label: (
        <Link
          to="/sourcer/help"
          style={{ fontSize: '16px' }}
        >
          Help & Support
        </Link>
      ),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined style={iconStyle} />,
      label: (
        <span style={{ fontSize: '16px' }}>{isLoggingOut ? <Spin size="small" /> : 'Logout'}</span>
      ),
      onClick: handleLogout,
    },
  ];

  const sidebarItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined style={iconStyle} />,
      label: <Link to="/sourcer/dashboard">Dashboard</Link>,
    },
    {
      key: 'candidates',
      icon: <TeamOutlined style={iconStyle} />,
      label: <Link to="/sourcer/candidates">Candidates</Link>,
    },
    {
      key: 'interviews',
      icon: <VideoCameraOutlined style={iconStyle} />,
      label: <Link to="/sourcer/interviews">Interviews</Link>,
    },
    {
      key: 'reports',
      icon: <FileTextOutlined style={iconStyle} />,
      label: <Link to="/sourcer/interview-reports">Reports</Link>,
    },
    {
      key: 'calendar',
      icon: <CalendarOutlined style={iconStyle} />,
      label: <Link to="/sourcer/calendar">Calendar</Link>,
    },
    {
      key: 'billing',
      icon: <DollarOutlined style={iconStyle} />,
      label: <Link to="/sourcer/billing">Billing</Link>,
    },
    {
      type: 'divider',
      style: { margin: '20px 0' },
    },
    {
      key: 'profile',
      icon: <UserOutlined style={iconStyle} />,
      label: <Link to="/sourcer/profile">Profile</Link>,
    },
    {
      key: 'setting',
      icon: <SettingOutlined style={iconStyle} />,
      label: <Link to="/sourcer/setting">Setting</Link>,
    },
    {
      key: 'Support',
      icon: <QuestionCircleOutlined style={iconStyle} />,
      label: <Link to="/sourcer/support">Support</Link>,
    },
  ];

  return (
    <Layout className="min-h-screen">
      <PageTitle title="Sourcer Dashboard" />
      <AppSidebar
        collapsed={collapsed}
        sidebarItems={sidebarItems}
        selectedKey={location.pathname.split('/')[2] || 'dashboard'}
        isDark={isDark}
        profile={profile}
        handleLogout={handleLogout}
        isLoggingOut={isLoggingOut}
      />

      {isMobile && (
        <MobileDrawer
          open={mobileDrawerOpen}
          onClose={toggleMobileDrawer}
          sidebarItems={sidebarItems}
          selectedKey={location.pathname.split('/')[2] || 'dashboard'}
          isDark={isDark}
          profile={profile}
          handleThemeToggle={handleThemeToggle}
          handleLogout={handleLogout}
          isLoggingOut={isLoggingOut}
        />
      )}

      <Layout
        style={{
          marginLeft: isMobile ? 0 : collapsed ? 80 : 250,
          transition: 'all 0.2s',
          minHeight: '100vh',
        }}
      >
        <AppHeader
          collapsed={collapsed}
          toggleCollapsed={toggleCollapsed}
          toggleMobileDrawer={toggleMobileDrawer}
          isMobile={isMobile}
          isTablet={isTablet}
          isDark={isDark}
          logo_dark={logo_dark}
          logo_lite={logo_lite}
          notificationItems={notificationItems}
          userMenuItems={userMenuItems}
          setSearchModalVisible={setSearchModalVisible}
          notifications={notifications}
          profile={profile}
          interviewItems={interviewItems}
          upcomingInterviews={upcomingInterviews}
          handleThemeToggle={handleThemeToggle}
          showInterviews={true}
        />

        <Content className={`p-2 sm:p-4`}>
          <div className="bg-card rounded-lg p-3 sm:p-4 shadow-sm">
            <Outlet />
          </div>
        </Content>

        <Footer
          style={{
            textAlign: 'center',
            padding: isMobile ? '8px 16px' : '12px 50px',
            fontSize: isMobile ? '14px' : '16px',
          }}
        >
          InterviewPro ©{new Date().getFullYear()} - Sourcer Dashboard
        </Footer>
      </Layout>

      <SearchModal
        visible={searchModalVisible}
        onClose={() => setSearchModalVisible(false)}
      />
    </Layout>
  );
};

export default InterviewerLayout;
