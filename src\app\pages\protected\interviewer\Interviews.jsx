import { useState, useMemo } from 'react';
import {
  Typography,
  Card,
  Tabs,
  List,
  Avatar,
  Button,
  Tag,
  Space,
  Empty,
  Badge,
  Divider,
} from 'antd';
import {
  VideoCameraOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useInterviews } from '@/features/interviewer/hooks';
import { Building } from 'lucide-react';
import InterviewRequestsList from '@/components/interview/InterviewRequestsList';
import LoadingWrapper from '@/components/common/LoadingWrapper';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Interviews = () => {
  const [activeTab, setActiveTab] = useState('requests');

  // Use the interviewer interviews hook
  const { upcoming, past, loading, error, forceRefresh } = useInterviews();

  const getStatusTag = (status) => {
    switch (status) {
      case 'scheduled':
        return <Tag color="blue">Scheduled</Tag>;
      case 'completed':
        return <Tag color="green">Completed</Tag>;
      case 'cancelled':
        return <Tag color="red">Cancelled</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const formatDate = (dateString) => {
    const options = {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    return `${Math.floor(minutes / 60)}h ${minutes % 60}m`;
  };

  // Memoized filtered interviews
  const filteredInterviews = useMemo(() => {
    switch (activeTab) {
      case 'upcoming':
        return upcoming;
      case 'past':
        return past;
      default:
        return [];
    }
  }, [activeTab, upcoming, past]);

  return (
    <div className="interviews-page">
      <Title
        level={2}
        className="mb-6"
      >
        Interview Management
      </Title>

      <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="mb-4"
        >
          <TabPane
            tab={
              <Badge
                count={0}
                offset={[10, 0]}
              >
                Requests
              </Badge>
            }
            key="requests"
          />
          <TabPane
            tab={
              <Badge
                count={upcoming?.length || 0}
                offset={[10, 0]}
              >
                Upcoming
              </Badge>
            }
            key="upcoming"
          />
          <TabPane
            tab="Past Interviews"
            key="past"
          />
        </Tabs>

        {activeTab === 'requests' ? (
          <InterviewRequestsList />
        ) : (
          <LoadingWrapper
            loading={loading}
            error={error}
            data={filteredInterviews}
            retryAction={forceRefresh}
            skeletonProps={{ paragraph: { rows: 5 } }}
          >
            {filteredInterviews.length > 0 ? (
              <List
                itemLayout="vertical"
                dataSource={filteredInterviews}
                renderItem={(interview) => (
                  <List.Item
                    key={interview.id}
                    actions={[
                      interview.status === 'scheduled' && interview.meeting_link && (
                        <Button
                          type="primary"
                          icon={<VideoCameraOutlined />}
                          className="bg-primary hover:bg-primary-hover"
                          onClick={() => window.open(interview.meeting_link, '_blank')}
                        >
                          Join Interview
                        </Button>
                      ),
                      interview.status === 'completed' && (
                        <Button type="default">View Feedback</Button>
                      ),
                    ].filter(Boolean)}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          size={64}
                          src={interview.candidate_profiles?.profile_photo_url}
                          icon={
                            !interview.candidate_profiles?.profile_photo_url && <UserOutlined />
                          }
                        />
                      }
                      title={
                        <Space
                          direction="vertical"
                          size={0}
                        >
                          <Text
                            strong
                            className="text-lg"
                          >
                            {interview.candidate_profiles?.full_name}
                          </Text>
                          <Text>{interview.jobs?.title}</Text>
                        </Space>
                      }
                      description={
                        <Space
                          direction="vertical"
                          size={4}
                          className="mt-2"
                        >
                          <Space>
                            <CalendarOutlined />
                            <Text>{formatDate(interview.interview_date)}</Text>
                          </Space>
                          <Space>
                            <ClockCircleOutlined />
                            <Text>Duration: {formatDuration(interview.duration_minutes)}</Text>
                          </Space>
                          <div className="mt-1">{getStatusTag(interview.status)}</div>
                        </Space>
                      }
                    />
                    <Divider className="my-3" />
                    <div className="flex items-center">
                      <Avatar
                        src={interview.company_profiles?.company_logo_url}
                        icon={
                          !interview.company_profiles?.company_logo_url && <Building size={20} />
                        }
                      />
                      <div className="ml-3">
                        <Text strong>Company: {interview.company_profiles?.company_name}</Text>
                        <div>
                          <Text type="secondary">Position: {interview.jobs?.title}</Text>
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
                pagination={{
                  pageSize: 5,
                }}
              />
            ) : (
              <Empty
                description={
                  <span>
                    {activeTab === 'upcoming'
                      ? 'No upcoming interviews scheduled.'
                      : 'No past interviews found.'}
                  </span>
                }
              />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};

export default Interviews;
