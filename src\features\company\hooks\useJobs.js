import { useEffect, useCallback, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import useCompanyStore from '../store/company.store';
import { companyDataService } from '../services';

/**
 * Optimized useJobs Hook for Companies
 *
 * Leverages standardized store patterns:
 * - Uses store cache for jobs and shortlisted candidates
 * - Utilizes standardized loading states
 * - Supports force refresh capabilities
 * - Integrates with store helper methods
 */
const useJobs = () => {
  const { user, profile } = useAuth();

  // Use store state and methods
  const {
    // Data from store
    jobs,
    shortlistedCandidates,

    // Loading states from store
    loading: storeLoading,
    jobsLoading,
    shortlistedLoading,

    // Error states from store
    error: storeError,
    jobsError,
    shortlistedError,

    // Store methods
    fetchCompanyJobs,
    fetchShortlistedCandidates,
    createJob,
    updateJob,
    deleteJob,
  } = useCompanyStore();

  // Combined loading and error states
  const loading = storeLoading || jobsLoading || shortlistedLoading;
  const error = storeError || jobsError || shortlistedError;

  // Local loading state for operations not tracked by store
  const [localLoading, setLocalLoading] = useState(false);

  // Optimized fetch function using data service with caching
  const fetchAllData = useCallback(
    async (forceRefresh = false) => {
      if (!user || !profile) return;

      try {
        // Use data service with cache management
        // Fetch jobs using data service
        const jobsData = await companyDataService.fetchCompanyJobs(profile.id, {}, forceRefresh);

        // Update store with fetched data
        if (jobsData && jobsData.length > 0) {
          // Update the store with the fetched data
          fetchCompanyJobs(profile.id, false); // Use store method to update state
        }

        // Fetch shortlisted candidates using store method
        await fetchShortlistedCandidates(profile.id);
      } catch (error) {
        console.error('Error fetching company data:', error);
      }
    },
    [profile, fetchCompanyJobs, fetchShortlistedCandidates, user]
  );

  // Effect to fetch data when user/profile changes
  useEffect(() => {
    if (user && profile) {
      fetchAllData();
    }
  }, [user, profile, fetchAllData]);

  // Enhanced job management methods
  const handleCreateJob = useCallback(
    async (jobData) => {
      if (!user || !profile) return null;

      setLocalLoading(true);
      try {
        const result = await createJob({ ...jobData, company_id: profile.id });

        // Invalidate jobs cache after creating a new job
        if (result) {
          dataFetchService.clearCache(profile.id, 'JOBS', 'company');
        }

        return result;
      } catch (error) {
        console.error('Error creating job:', error);
        return null;
      } finally {
        setLocalLoading(false);
      }
    },
    [createJob, profile, user]
  );

  const handleUpdateJob = useCallback(
    async (jobId, updates) => {
      if (!user || !profile) return null;

      setLocalLoading(true);
      try {
        const result = await updateJob(jobId, updates);

        // Invalidate jobs cache after updating a job
        if (result) {
          dataFetchService.clearCache(profile.id, 'JOBS', 'company');
        }

        return result;
      } catch (error) {
        console.error('Error updating job:', error);
        return null;
      } finally {
        setLocalLoading(false);
      }
    },
    [updateJob, profile, user]
  );

  const handleDeleteJob = useCallback(
    async (jobId) => {
      if (!user || !profile) return false;

      setLocalLoading(true);
      try {
        const result = await deleteJob(jobId);

        // Invalidate jobs cache after deleting a job
        if (result) {
          dataFetchService.clearCache(profile.id, 'JOBS', 'company');
          dataFetchService.clearCache(profile.id, 'APPLICATIONS', 'company');
        }

        return result;
      } catch (error) {
        console.error('Error deleting job:', error);
        return false;
      } finally {
        setLocalLoading(false);
      }
    },
    [deleteJob, profile, user]
  );

  return {
    // Data
    jobs,
    shortlistedCandidates,

    // UI states
    loading: loading || localLoading,
    error,

    // Actions
    fetchAllData,
    handleCreateJob,
    handleUpdateJob,
    handleDeleteJob,

    // Direct access to cached data methods
    fetchJobsFromCache: (forceRefresh = false) =>
      companyDataService.fetchCompanyJobs(profile?.id, {}, forceRefresh),
    fetchApplicationsFromCache: (jobId = null, forceRefresh = false) =>
      companyDataService.fetchCompanyApplications(profile?.id, jobId, forceRefresh),
  };
};

export default useJobs;
