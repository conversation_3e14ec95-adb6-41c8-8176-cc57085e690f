import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Tabs,
  List,
  Avatar,
  Button,
  Tag,
  Space,
  Empty,
  Skeleton,
  Badge,
  Tooltip,
  Divider,
} from 'antd';
import {
  VideoCameraOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  BuildOutlined,
} from '@ant-design/icons';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import { Building, Video } from 'lucide-react';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Interviews = () => {
  const [interviews, setInterviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('upcoming');
  const { user } = useAuth();

  useEffect(() => {
    fetchInterviews();
  }, [user]);

  const fetchInterviews = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Fetch interviews with job, company, and interviewer details
      const { data, error } = await supabase
        .from('interviews')
        .select(
          `
          *,
          jobs:job_id (
            id,
            title
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          ),
          interviewer_profiles:interviewer_id (
            full_name,
            current_designation,
            profile_photo_url
          )
        `
        )
        .eq('candidate_id', user.id)
        .order('interview_date', { ascending: true });

      if (error) throw error;

      setInterviews(data || []);
    } catch (error) {
      console.error('Error fetching interviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status) => {
    switch (status) {
      case 'requested':
        return (
          <Tag
            icon={<ClockCircleOutlined />}
            color="blue"
          >
            Requested
          </Tag>
        );
      case 'scheduled':
        return (
          <Tag
            icon={<CalendarOutlined />}
            color="orange"
          >
            Scheduled
          </Tag>
        );
      case 'completed':
        return (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
          >
            Completed
          </Tag>
        );
      case 'cancelled':
        return (
          <Tag
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Cancelled
          </Tag>
        );
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const isUpcoming = (interview) => {
    return ['requested', 'scheduled'].includes(interview.status);
  };

  const isPast = (interview) => {
    return ['completed', 'cancelled'].includes(interview.status);
  };

  const filteredInterviews =
    activeTab === 'upcoming'
      ? interviews.filter(isUpcoming)
      : activeTab === 'past'
        ? interviews.filter(isPast)
        : interviews;

  const formatDate = (dateString) => {
    const options = {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatDuration = (minutes) => {
    return `${Math.floor(minutes / 60)}h ${minutes % 60}m`;
  };

  return (
    <div className="interviews-page">
      <Title
        level={2}
        className="mb-6"
      >
        My Interviews
      </Title>

      <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="mb-4"
        >
          <TabPane
            tab={
              <Badge
                count={interviews.filter(isUpcoming).length}
                offset={[10, 0]}
              >
                Upcoming
              </Badge>
            }
            key="upcoming"
          />
          <TabPane
            tab="Past Interviews"
            key="past"
          />
          <TabPane
            tab="All Interviews"
            key="all"
          />
        </Tabs>

        {loading ? (
          <Skeleton
            active
            paragraph={{ rows: 5 }}
          />
        ) : filteredInterviews.length > 0 ? (
          <List
            itemLayout="vertical"
            dataSource={filteredInterviews}
            renderItem={(interview) => (
              <List.Item
                key={interview.id}
                actions={[
                  interview.status === 'scheduled' && (
                    <Button
                      type="primary"
                      icon={<VideoCameraOutlined />}
                      className="bg-primary hover:bg-primary-hover"
                    >
                      Join Interview
                    </Button>
                  ),
                  interview.status === 'requested' && (
                    <Button
                      type="primary"
                      ghost
                    >
                      Confirm Schedule
                    </Button>
                  ),
                  interview.status === 'completed' && <Button type="default">View Feedback</Button>,
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size={64}
                      src={interview.company_profiles?.company_logo_url}
                      icon={!interview.company_profiles?.company_logo_url && <Building size={32} />}
                    />
                  }
                  title={
                    <Space
                      direction="vertical"
                      size={0}
                    >
                      <Text
                        strong
                        className="text-lg"
                      >
                        {interview.jobs?.title}
                      </Text>
                      <Text>{interview.company_profiles?.company_name}</Text>
                    </Space>
                  }
                  description={
                    <Space
                      direction="vertical"
                      size={4}
                      className="mt-2"
                    >
                      <Space>
                        <CalendarOutlined />
                        <Text>{formatDate(interview.interview_date)}</Text>
                      </Space>
                      <Space>
                        <ClockCircleOutlined />
                        <Text>Duration: {formatDuration(interview.duration_minutes)}</Text>
                      </Space>
                      <div className="mt-1">{getStatusTag(interview.status)}</div>
                    </Space>
                  }
                />
                <Divider className="my-3" />
                <div className="flex items-center">
                  <Avatar
                    src={interview.interviewer_profiles?.profile_photo_url}
                    icon={!interview.interviewer_profiles?.profile_photo_url && <User size={16} />}
                  />
                  <div className="ml-3">
                    <Text strong>Interviewer: {interview.interviewer_profiles?.full_name}</Text>
                    <div>
                      <Text type="secondary">
                        {interview.interviewer_profiles?.current_designation}
                      </Text>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
            pagination={{
              pageSize: 5,
            }}
          />
        ) : (
          <Empty
            description={
              <span>No interviews found. Your scheduled interviews will appear here.</span>
            }
          />
        )}
      </Card>
    </div>
  );
};

export default Interviews;
