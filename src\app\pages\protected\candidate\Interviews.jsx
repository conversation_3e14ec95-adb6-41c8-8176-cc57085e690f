import { useState, useMemo } from 'react';
import {
  Typography,
  Card,
  Tabs,
  List,
  Avatar,
  Button,
  Tag,
  Space,
  Empty,
  Skeleton,
  Badge,
  Divider,
} from 'antd';
import {
  VideoCameraOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  LinkOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';
import { useInterviews } from '@/features/candidate/hooks';
import { Building } from 'lucide-react';
import CreateInvitation from '@/components/interview/CreateInvitation';
import { RequestInterviewForm } from '@/features/candidate/components/interviews';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Interviews = () => {
  const [activeTab, setActiveTab] = useState('upcoming');
  const [showCreateInvitation, setShowCreateInvitation] = useState(false);
  const [showCreateRequest, setShowCreateRequest] = useState(false);

  // Use the optimized interviews hook
  const {
    interviews,
    interviewRequests,
    upcomingInterviews,
    pastInterviews,
    loading,
    getInterviewStats,
    refetch,
  } = useInterviews();

  const getStatusTag = (status) => {
    switch (status) {
      case 'requested':
        return (
          <Tag
            icon={<ClockCircleOutlined />}
            color="blue"
          >
            Requested
          </Tag>
        );
      case 'scheduled':
        return (
          <Tag
            icon={<CalendarOutlined />}
            color="orange"
          >
            Scheduled
          </Tag>
        );
      case 'completed':
        return (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
          >
            Completed
          </Tag>
        );
      case 'cancelled':
        return (
          <Tag
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Cancelled
          </Tag>
        );
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  // Memoized filtered interviews to prevent unnecessary re-calculations
  const filteredInterviews = useMemo(() => {
    switch (activeTab) {
      case 'requested':
        return interviewRequests;
      case 'upcoming':
        return upcomingInterviews;
      case 'past':
        return pastInterviews;
      default:
        return interviews;
    }
  }, [activeTab, interviewRequests, upcomingInterviews, pastInterviews, interviews]);

  // Memoized stats to prevent unnecessary re-calculations
  const interviewStats = useMemo(() => getInterviewStats(), [getInterviewStats]);

  const formatDate = (dateString) => {
    const options = {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatDuration = (minutes) => {
    return `${Math.floor(minutes / 60)}h ${minutes % 60}m`;
  };

  // Show inline request form when creating request
  if (showCreateRequest) {
    return (
      <div className="interviews-page">
        <div className="flex items-center mb-6">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => setShowCreateRequest(false)}
            className="mr-4"
          >
            Back to Interviews
          </Button>
          <Title
            level={2}
            className="mb-0"
          >
            Request Interview
          </Title>
        </div>

        <RequestInterviewForm
          onSuccess={(result) => {
            setShowCreateRequest(false);
            refetch();
            return result;
          }}
          onCancel={() => setShowCreateRequest(false)}
        />
      </div>
    );
  }

  return (
    <div className="interviews-page">
      <div className="flex justify-between items-center mb-6">
        <Title
          level={2}
          className="mb-0"
        >
          My Interviews
        </Title>
        <Button
          type="primary"
          icon={<CalendarOutlined />}
          onClick={() => setShowCreateRequest(true)}
          size="large"
        >
          Request Interview
        </Button>
      </div>

      <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="mb-4"
        >
          <TabPane
            tab={
              <Badge
                count={interviewStats.requested}
                offset={[10, 0]}
              >
                Requested
              </Badge>
            }
            key="requested"
          />
          <TabPane
            tab={
              <Badge
                count={interviewStats.upcoming}
                offset={[10, 0]}
              >
                Upcoming
              </Badge>
            }
            key="upcoming"
          />
          <TabPane
            tab="Past Interviews"
            key="past"
          />
          <TabPane
            tab="All Interviews"
            key="all"
          />
        </Tabs>

        {loading ? (
          <Skeleton
            active
            paragraph={{ rows: 5 }}
          />
        ) : filteredInterviews.length > 0 ? (
          <List
            itemLayout="vertical"
            dataSource={filteredInterviews}
            renderItem={(interview) => (
              <List.Item
                key={interview.id}
                actions={[
                  interview.status === 'scheduled' && interview.meeting_link && (
                    <Button
                      type="primary"
                      icon={<VideoCameraOutlined />}
                      className="bg-primary hover:bg-primary-hover"
                      onClick={() => window.open(interview.meeting_link, '_blank')}
                    >
                      Join Interview
                    </Button>
                  ),
                  interview.status === 'scheduled' && !interview.meeting_link && (
                    <Button
                      type="primary"
                      icon={<LinkOutlined />}
                      onClick={() => setShowCreateInvitation(true)}
                    >
                      Create Invitation
                    </Button>
                  ),
                  interview.status === 'requested' && (
                    <Button
                      type="primary"
                      ghost
                    >
                      Confirm Schedule
                    </Button>
                  ),
                  interview.status === 'completed' && <Button type="default">View Feedback</Button>,
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size={64}
                      src={interview.company_profiles?.company_logo_url}
                      icon={!interview.company_profiles?.company_logo_url && <Building size={32} />}
                    />
                  }
                  title={
                    <Space
                      direction="vertical"
                      size={0}
                    >
                      <Text
                        strong
                        className="text-lg"
                      >
                        {interview.jobs?.title}
                      </Text>
                      <Text>{interview.company_profiles?.company_name}</Text>
                    </Space>
                  }
                  description={
                    <Space
                      direction="vertical"
                      size={4}
                      className="mt-2"
                    >
                      <Space>
                        <CalendarOutlined />
                        <Text>{formatDate(interview.interview_date)}</Text>
                      </Space>
                      <Space>
                        <ClockCircleOutlined />
                        <Text>Duration: {formatDuration(interview.duration_minutes)}</Text>
                      </Space>
                      <div className="mt-1">{getStatusTag(interview.status)}</div>
                    </Space>
                  }
                />
                <Divider className="my-3" />
                <div className="flex items-center">
                  <Avatar
                    src={interview.interviewer_profiles?.profile_photo_url}
                    icon={!interview.interviewer_profiles?.profile_photo_url && <UserOutlined />}
                  />
                  <div className="ml-3">
                    <Text strong>Interviewer: {interview.interviewer_profiles?.full_name}</Text>
                    <div>
                      <Text type="secondary">
                        {interview.interviewer_profiles?.current_designation}
                      </Text>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
            pagination={{
              pageSize: 5,
            }}
          />
        ) : (
          <Empty
            description={
              <span>No interviews found. Your scheduled interviews will appear here.</span>
            }
          />
        )}
      </Card>

      {/* Create Invitation Modal */}
      <CreateInvitation
        visible={showCreateInvitation}
        onCancel={() => setShowCreateInvitation(false)}
        onSuccess={() => {
          setShowCreateInvitation(false);
          refetch(); // Refresh interviews list
        }}
      />
    </div>
  );
};

export default Interviews;
