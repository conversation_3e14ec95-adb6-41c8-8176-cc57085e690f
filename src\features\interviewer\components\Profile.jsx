import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Divider,
  Upload,
  Select,
  InputNumber,
  Spin,
  message,
  Avatar,
  Tag,
  Row,
  Col,
  Switch,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  UploadOutlined,
  LinkedinOutlined,
  BankOutlined,
  DollarOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons';
import { uploadProfilePhoto } from '../services/interviewer.service';
import useAuth from '@/hooks/useAuth';
import { INTERVIEWER_ROLES } from '@/utils/constants';

const { Title, Text } = Typography;
const { Option } = Select;

const Profile = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [photoFile, setPhotoFile] = useState(null);
  const { user, profile, saveProfile } = useAuth();

  useEffect(() => {
    if (profile) {
      // Transform preferred roles for form
      const preferredRoles = profile.preferred_interview_roles
        ? profile.preferred_interview_roles.map((item) => item.role)
        : [];

      form.setFieldsValue({
        ...profile,
        preferred_roles: preferredRoles,
      });
      setLoading(false);
    }
  }, [profile, form]);

  const handleSubmit = async (values) => {
    setSaving(true);
    try {
      // Transform preferred roles to the expected format
      const preferredInterviewRoles = values.preferred_roles.map((role) => ({ role }));

      const updatedProfile = {
        ...values,
        preferred_interview_roles: preferredInterviewRoles,
      };

      // Remove the form field that doesn't match the database schema
      delete updatedProfile.preferred_roles;

      const result = await saveProfile(user.id, updatedProfile);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile');
      }

      // Upload profile photo if provided
      if (photoFile) {
        const { success: photoSuccess, error: photoError } = await uploadProfilePhoto(
          user.id,
          photoFile
        );
        if (!photoSuccess) {
          console.error('Photo upload failed:', photoError);
        }
      }

      message.success('Profile updated successfully');
    } catch (error) {
      console.error('Profile update error:', error);
      message.error(error.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const beforePhotoUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG file!');
      return Upload.LIST_IGNORE;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must be smaller than 2MB!');
      return Upload.LIST_IGNORE;
    }

    setPhotoFile(file);
    return false; // Prevent auto upload
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="interviewer-profile">
      <Card className="mb-6">
        <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
          <Avatar
            size={100}
            src={profile?.profile_photo_url}
            icon={!profile?.profile_photo_url && <UserOutlined />}
          />
          <div className="flex-1">
            <Title level={3}>{profile?.full_name || 'Complete Your Profile'}</Title>
            <Text type="secondary">
              {profile?.current_designation || 'Job Title'}{' '}
              {profile?.current_company && `at ${profile.current_company}`}
            </Text>

            <div className="mt-3 flex flex-wrap gap-2">
              {profile?.years_experience && (
                <Tag color="green">{profile.years_experience} years experience</Tag>
              )}
              {profile?.performance_rating > 0 && (
                <Tag color="gold">Rating: {profile.performance_rating}/5</Tag>
              )}
              {profile?.equipment_confirmation && <Tag color="blue">Equipment Verified</Tag>}
            </div>
          </div>
        </div>
      </Card>

      <Card title="Edit Interviewer Profile">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={profile || {}}
        >
          <Divider orientation="left">Basic Information</Divider>
          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="full_name"
                label="Full Name"
                rules={[{ required: true, message: 'Please enter your full name' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="Full Name"
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="professional_email"
                label="Professional Email"
                rules={[
                  { required: true, message: 'Please enter your email' },
                  { type: 'email', message: 'Please enter a valid email' },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="Professional Email"
                  disabled
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="mobile_number"
                label="Mobile Number"
                rules={[
                  { required: true, message: 'Please enter your mobile number' },
                  {
                    pattern: /^[0-9]{10}$/,
                    message: 'Please enter a valid 10-digit mobile number',
                  },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="Mobile Number"
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="linkedin_url"
                label="LinkedIn Profile URL"
                rules={[{ type: 'url', message: 'Please enter a valid URL' }]}
              >
                <Input
                  prefix={<LinkedinOutlined />}
                  placeholder="LinkedIn Profile URL"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">Professional Information</Divider>
          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="current_designation"
                label="Current Designation"
                rules={[{ required: true, message: 'Please enter your current designation' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="Current Designation"
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="current_company"
                label="Current Company"
                rules={[{ required: true, message: 'Please enter your current company' }]}
              >
                <Input
                  prefix={<BankOutlined />}
                  placeholder="Current Company"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="years_experience"
                label="Years of Industry Experience"
                rules={[{ required: true, message: 'Please enter your years of experience' }]}
              >
                <InputNumber
                  min={1}
                  max={50}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="equipment_confirmation"
                label="Audio/Video Equipment Confirmation"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="Verified"
                  unCheckedChildren="Not Verified"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="preferred_roles"
            label="Preferred Interview Roles"
            rules={[{ required: true, message: 'Please select at least one role' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select roles you can interview for"
              className="w-full"
            >
              {INTERVIEWER_ROLES.map((role) => (
                <Option
                  key={role}
                  value={role}
                >
                  {role}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="profile_photo"
            label="Profile Photo"
          >
            <Upload
              name="profile_photo"
              listType="picture"
              beforeUpload={beforePhotoUpload}
              maxCount={1}
              showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
            >
              <Button icon={<UploadOutlined />}>Upload Photo</Button>
            </Upload>
          </Form.Item>

          <Divider orientation="left">Payment Details</Divider>
          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="payment_details.accountHolderName"
                label="Account Holder Name"
              >
                <Input placeholder="Account Holder Name" />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="payment_details.accountNumber"
                label="Account Number"
              >
                <Input placeholder="Account Number" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="payment_details.ifscCode"
                label="IFSC Code"
              >
                <Input placeholder="IFSC Code" />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="payment_details.bankName"
                label="Bank Name"
              >
                <Input placeholder="Bank Name" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="payment_details.upiId"
            label="UPI ID"
          >
            <Input
              prefix={<DollarOutlined />}
              placeholder="UPI ID"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={saving}
            >
              Save Profile
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Profile;
