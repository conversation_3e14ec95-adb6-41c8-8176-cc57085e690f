import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useInterviewerStore from '../store/interviewer.store';

/**
 * Optimized useInterviews Hook for Interviewers
 *
 * Leverages standardized store patterns:
 * - Uses store cache for interview requests and scheduled interviews
 * - Utilizes standardized loading states
 * - Supports force refresh capabilities
 * - Integrates with store helper methods
 */
const useInterviews = () => {
  // Local state for categorized interviews (dashboard-specific)
  const [past, setPast] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);

  const { user, profile } = useAuth();

  // Use store state and methods
  const {
    // Data from store
    interviewRequests,
    scheduledInterviews,
    completedInterviews,

    // Loading states from store
    loading: storeLoading,
    requestsLoading,
    scheduledLoading,
    completedLoading,

    // Error states from store
    error: storeError,
    requestsError,
    scheduledError,
    completedError,

    // Store methods
    fetchInterviewRequests,
    fetchScheduledInterviews,
    fetchCompletedInterviews,
    acceptInterviewRequest,
    scheduleInterview,
    completeInterview,
  } = useInterviewerStore();

  // Combined loading and error states
  const loading =
    storeLoading || requestsLoading || scheduledLoading || completedLoading || localLoading;
  const error = storeError || requestsError || scheduledError || completedError;

  // Update past interviews from completed interviews store data
  useEffect(() => {
    if (completedInterviews) {
      setPast(completedInterviews);
    }
  }, [completedInterviews]);

  // Optimized fetch function using store methods
  const fetchAllInterviews = useCallback(
    async (forceRefresh = false) => {
      if (!user || !profile?.id) return;

      try {
        // Use store methods with cache management
        await Promise.all([
          fetchInterviewRequests(profile.id, forceRefresh),
          fetchScheduledInterviews(profile.id, forceRefresh),
          fetchCompletedInterviews(profile.id, forceRefresh),
        ]);
      } catch (err) {
        console.error('Error fetching interviewer interviews:', err);
      }
    },
    [
      user?.id,
      profile?.id,
      fetchInterviewRequests,
      fetchScheduledInterviews,
      fetchCompletedInterviews,
    ]
  );

  // Effect to fetch data when user/profile changes
  useEffect(() => {
    if (user?.id && profile?.id) {
      fetchAllInterviews();
    }
  }, [user?.id, profile?.id, fetchAllInterviews]);

  // Enhanced interview management methods
  const handleAcceptRequest = useCallback(
    async (requestId, scheduleData) => {
      const result = await acceptInterviewRequest(requestId, scheduleData);

      // Refresh data after accepting request
      if (result.success) {
        await fetchAllInterviews(true);
      }

      return result;
    },
    [acceptInterviewRequest, fetchAllInterviews]
  );

  const handleScheduleInterview = useCallback(
    async (interviewId, scheduleData) => {
      const result = await scheduleInterview(interviewId, scheduleData);

      // Refresh data after scheduling
      if (result.success) {
        await fetchAllInterviews(true);
      }

      return result;
    },
    [scheduleInterview, fetchAllInterviews]
  );

  const handleCompleteInterview = useCallback(
    async (interviewId, completionData) => {
      const result = await completeInterview(interviewId, completionData);

      // Refresh data after completion
      if (result.success) {
        await fetchAllInterviews(true);
      }

      return result;
    },
    [completeInterview, fetchAllInterviews]
  );

  return {
    // Interview data from store
    upcoming: scheduledInterviews || [],
    past: completedInterviews || [],
    requests: interviewRequests || [],

    // Combined UI state
    loading,
    error,

    // Actions
    refetch: fetchAllInterviews,

    // Enhanced interview management
    acceptRequest: handleAcceptRequest,
    scheduleInterview: handleScheduleInterview,
    completeInterview: handleCompleteInterview,

    // Force refresh capabilities
    forceRefresh: () => fetchAllInterviews(true),
    refreshRequests: () => fetchInterviewRequests(profile?.id, true),
    refreshScheduled: () => fetchScheduledInterviews(profile?.id, true),
    refreshCompleted: () => fetchCompletedInterviews(profile?.id, true),
  };
};

export default useInterviews;
