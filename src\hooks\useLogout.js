import { useNavigate } from 'react-router-dom';
import useAuth from './useAuth';
import showToast from '@/utils/toast';

/**
 * Enhanced logout hook that provides navigation and toast notifications
 * Uses the loading state from useAuth for consistency
 *
 * @param {Object} options - Configuration options
 * @param {string} options.redirectTo - Where to redirect after logout (default: '/login')
 * @param {boolean} options.showToast - Whether to show toast notifications (default: true)
 * @param {boolean} options.forceReload - Whether to force page reload after logout (default: false)
 */
const useLogout = (options = {}) => {
  const { redirectTo = '/login', showToast: enableToast = true, forceReload = false } = options;

  const navigate = useNavigate();
  const { logout, loading } = useAuth();

  /**
   * Enhanced logout handler with navigation and notifications
   * @returns {Promise<void>}
   */
  const handleLogout = async () => {
    try {
      console.log('Logout called,', loading);
      const { success, message } = await logout();
      console.log('Logout success,', success, message);

      if (success) {
        if (enableToast) {
          showToast.success('Logged out successfully');
        }

        if (forceReload) {
          window.location.href = redirectTo;
        } else {
          navigate(redirectTo, { replace: true });
        }
      } else {
        throw new Error(message || 'Logout failed');
      }
    } catch (error) {
      console.error('Logout error:', error);
      if (enableToast) {
        showToast.error('Failed to logout: ' + (error.message || 'Unknown error'));
      }
      // Still redirect on error to prevent stuck state
      if (forceReload) {
        window.location.href = redirectTo;
      } else {
        navigate(redirectTo, { replace: true });
      }
    }
  };

  return {
    handleLogout,
    isLoggingOut: loading, // Use loading state from useAuth
  };
};

export default useLogout;
