/**
 * CreateInvitation Component
 *
 * Modal component for creating interview invitations
 * Can be used by any role to invite participants to interviews
 */

import { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  InputNumber,
  Button,
  Space,
  Typography,
  Alert,
  message,
  Divider,
  Card,
} from 'antd';
import { MailOutlined, UserOutlined, LinkOutlined, CopyOutlined } from '@ant-design/icons';
import {
  createInterviewInvitation,
  sendInvitationNotification,
} from '@/services/interviewInvitation.service';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CreateInvitation = ({ visible, onCancel, onSuccess, interviewId, defaultData = {} }) => {
  const [form] = Form.useForm();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [invitationLink, setInvitationLink] = useState('');
  const [showLink, setShowLink] = useState(false);

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      const invitationData = {
        interviewId: interviewId || values.interviewId,
        invitedBy: user.id,
        invitedRole: profile.role,
        recipientEmail: values.recipientEmail,
        recipientName: values.recipientName,
        scheduledDate: values.scheduledDate.toISOString(),
        duration: values.duration,
        message: values.message,
        interviewType: values.interviewType,
      };

      const result = await createInterviewInvitation(invitationData);

      if (result.success) {
        setInvitationLink(result.invitationLink);
        setShowLink(true);

        // Send notification if requested
        if (values.sendNotification) {
          await sendInvitationNotification(result.invitation, result.invitationLink);
          message.success('Invitation created and sent successfully!');
        } else {
          message.success('Invitation created successfully!');
        }

        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        message.error(result.error || 'Failed to create invitation');
      }
    } catch (error) {
      console.error('Error creating invitation:', error);
      message.error('Failed to create invitation');
    } finally {
      setLoading(false);
    }
  };

  // Copy invitation link
  const copyInvitationLink = () => {
    navigator.clipboard.writeText(invitationLink);
    message.success('Invitation link copied to clipboard!');
  };

  // Reset form and state
  const handleCancel = () => {
    form.resetFields();
    setInvitationLink('');
    setShowLink(false);
    onCancel();
  };

  // Handle modal close after success
  const handleClose = () => {
    handleCancel();
  };

  return (
    <Modal
      title="Create Interview Invitation"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      {!showLink ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            interviewType: 'video',
            duration: 60,
            sendNotification: true,
            ...defaultData,
          }}
        >
          <Alert
            message="Create Interview Invitation"
            description="Fill in the details below to create a secure interview invitation link that can be shared with participants."
            type="info"
            showIcon
            className="mb-6"
          />

          <Form.Item
            name="recipientName"
            label="Recipient Name"
            rules={[{ required: true, message: 'Please enter recipient name' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Enter participant's full name"
            />
          </Form.Item>

          <Form.Item
            name="recipientEmail"
            label="Recipient Email"
            rules={[
              { required: true, message: 'Please enter recipient email' },
              { type: 'email', message: 'Please enter a valid email' },
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="Enter participant's email address"
            />
          </Form.Item>

          <Form.Item
            name="scheduledDate"
            label="Interview Date & Time"
            rules={[{ required: true, message: 'Please select interview date and time' }]}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              placeholder="Select date and time"
              disabledDate={(current) => current && current < dayjs().startOf('day')}
              className="w-full"
            />
          </Form.Item>

          <Form.Item
            name="duration"
            label="Duration (minutes)"
            rules={[{ required: true, message: 'Please enter duration' }]}
          >
            <InputNumber
              min={15}
              max={180}
              step={15}
              placeholder="60"
              className="w-full"
              addonAfter="minutes"
            />
          </Form.Item>

          <Form.Item
            name="interviewType"
            label="Interview Type"
            rules={[{ required: true, message: 'Please select interview type' }]}
          >
            <Select placeholder="Select interview type">
              <Option value="video">Video Call</Option>
              <Option value="phone">Phone Call</Option>
              <Option value="in-person">In-Person</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="message"
            label="Personal Message (Optional)"
          >
            <TextArea
              rows={3}
              placeholder="Add a personal message for the participant..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Divider />

          <Form.Item className="mb-0">
            <Space className="w-full justify-between">
              <Button onClick={handleCancel}>Cancel</Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<LinkOutlined />}
              >
                Create Invitation
              </Button>
            </Space>
          </Form.Item>
        </Form>
      ) : (
        <div>
          <Alert
            message="Invitation Created Successfully!"
            description="Your interview invitation has been created. Share the link below with the participant."
            type="success"
            showIcon
            className="mb-6"
          />

          <Card className="mb-6">
            <Title level={5}>
              <LinkOutlined /> Invitation Link
            </Title>
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded border">
              <Text
                code
                copyable={{ text: invitationLink }}
              >
                {invitationLink}
              </Text>
            </div>
            <div className="mt-3">
              <Button
                type="primary"
                icon={<CopyOutlined />}
                onClick={copyInvitationLink}
                block
              >
                Copy Invitation Link
              </Button>
            </div>
          </Card>

          <Alert
            message="Important Notes"
            description={
              <ul className="mb-0 pl-4">
                <li>The invitation link is valid for 24 hours</li>
                <li>Participants can join the interview room using this link</li>
                <li>The interview will be recorded for quality purposes</li>
                <li>Both participants will receive email notifications</li>
              </ul>
            }
            type="info"
            showIcon
            className="mb-6"
          />

          <div className="text-center">
            <Button
              type="primary"
              onClick={handleClose}
            >
              Done
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default CreateInvitation;
