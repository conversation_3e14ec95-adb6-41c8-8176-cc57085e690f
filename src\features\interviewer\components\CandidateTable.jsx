import React from 'react';
import { Card, Typography, Table, Tag, Avatar, Button, Space, Tooltip } from 'antd';
import { FilterOutlined, SortAscendingOutlined, UserOutlined } from '@ant-design/icons';
import { useColorModeStore } from '@/store/colorMode.store';

const { Title } = Typography;

const CandidateTable = ({ title, data, loading = false, onFilter, onSort }) => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const columns = [
    {
      title: 'Candidate Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div className="flex items-center">
          <Avatar
            src={record.avatar}
            icon={!record.avatar && <UserOutlined />}
            size="small"
            className="mr-2"
          />
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: 'Score',
      dataIndex: 'score',
      key: 'score',
      sorter: (a, b) => a.score - b.score,
      render: (score) => (
        <Tag color={score >= 80 ? 'green' : score >= 60 ? 'blue' : 'orange'}>{score}%</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let color = 'default';
        if (status === 'Permanent') color = 'success';
        if (status === 'Contract') color = 'processing';
        if (status === 'Rejected') color = 'error';
        if (status === 'Pending') color = 'warning';

        return <Tag color={color}>{status}</Tag>;
      },
    },
  ];

  return (
    <Card
      className="table-card h-full transition-all hover:shadow-md"
      style={{
        borderRadius: '12px',
        borderColor: 'transparent',
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.04)' : 'white',
      }}
      styles={{ body: { padding: '16px' } }}
    >
      <div className="flex items-center justify-between mb-4">
        <Title
          level={5}
          className="m-0"
        >
          {title}
        </Title>
        <Space>
          <Tooltip title="Filter">
            <Button
              icon={<FilterOutlined />}
              size="small"
              onClick={onFilter}
            />
          </Tooltip>
          <Tooltip title="Sort">
            <Button
              icon={<SortAscendingOutlined />}
              size="small"
              onClick={onSort}
            />
          </Tooltip>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={{ pageSize: 5 }}
        size="small"
        rowKey="id"
      />
    </Card>
  );
};

export default CandidateTable;
