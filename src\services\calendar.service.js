/**
 * Calendar Service
 * 
 * Service for managing calendar events and integrating interviews with calendar systems
 * Provides functions to convert interviews to calendar events and sync with external calendars
 */

import { supabase } from '@/utils/supabaseClient';
import dayjs from 'dayjs';

/**
 * Convert interview data to calendar event format
 * @param {Object} interview - Interview object
 * @param {string} userRole - Role of the user (candidate, interviewer, company)
 * @returns {Object} Calendar event object
 */
export const interviewToCalendarEvent = (interview, userRole = 'candidate') => {
  const startDate = dayjs(interview.interview_date || interview.preferred_date);
  const endDate = startDate.add(interview.duration_minutes || 60, 'minute');

  // Determine event title based on user role
  let title = '';
  let description = '';
  
  switch (userRole) {
    case 'candidate':
      title = `Interview: ${interview.jobs?.title}`;
      description = `Interview with ${interview.company_profiles?.company_name}`;
      if (interview.interviewer_profiles?.full_name) {
        description += `\nInterviewer: ${interview.interviewer_profiles.full_name}`;
      }
      break;
    case 'interviewer':
      title = `Interview: ${interview.candidates?.full_name}`;
      description = `Interview for ${interview.jobs?.title} position`;
      if (interview.company_profiles?.company_name) {
        description += `\nCompany: ${interview.company_profiles.company_name}`;
      }
      break;
    case 'company':
      title = `Interview: ${interview.candidates?.full_name} - ${interview.jobs?.title}`;
      description = `Interview session`;
      if (interview.interviewer_profiles?.full_name) {
        description += `\nInterviewer: ${interview.interviewer_profiles.full_name}`;
      }
      break;
    default:
      title = 'Interview Session';
      description = 'Interview session';
  }

  // Add meeting link if available
  if (interview.meeting_link) {
    description += `\n\nJoin Meeting: ${interview.meeting_link}`;
  }

  // Add interview type and status
  description += `\nType: ${interview.interview_type || 'video'}`;
  description += `\nStatus: ${interview.status}`;

  // Add message if available
  if (interview.message) {
    description += `\n\nMessage: ${interview.message}`;
  }

  return {
    id: interview.id,
    title,
    description,
    start: startDate.toDate(),
    end: endDate.toDate(),
    date: startDate.format('YYYY-MM-DD'),
    time: startDate.format('HH:mm'),
    duration: interview.duration_minutes || 60,
    type: 'interview',
    status: interview.status,
    color: getEventColor(interview.status),
    interview_type: interview.interview_type || 'video',
    meeting_link: interview.meeting_link,
    participants: getParticipants(interview, userRole),
    metadata: {
      interview_id: interview.id,
      job_id: interview.job_id,
      candidate_id: interview.candidate_id,
      interviewer_id: interview.interviewer_id,
      company_id: interview.company_id,
    },
  };
};

/**
 * Get event color based on interview status
 * @param {string} status - Interview status
 * @returns {string} Color code
 */
const getEventColor = (status) => {
  switch (status) {
    case 'requested':
      return '#faad14'; // Orange
    case 'scheduled':
      return '#1890ff'; // Blue
    case 'completed':
      return '#52c41a'; // Green
    case 'cancelled':
      return '#ff4d4f'; // Red
    case 'declined':
      return '#d9d9d9'; // Gray
    default:
      return '#722ed1'; // Purple
  }
};

/**
 * Get participants list for the event
 * @param {Object} interview - Interview object
 * @param {string} userRole - Current user role
 * @returns {Array} Participants array
 */
const getParticipants = (interview, userRole) => {
  const participants = [];

  if (interview.candidates) {
    participants.push({
      name: interview.candidates.full_name,
      email: interview.candidates.email,
      role: 'candidate',
      avatar: interview.candidates.profile_photo_url,
    });
  }

  if (interview.interviewer_profiles) {
    participants.push({
      name: interview.interviewer_profiles.full_name,
      email: interview.interviewer_profiles.email,
      role: 'interviewer',
      avatar: interview.interviewer_profiles.profile_photo_url,
    });
  }

  return participants;
};

/**
 * Get calendar events for a user
 * @param {string} userId - User ID
 * @param {string} userRole - User role
 * @param {Object} dateRange - Date range filter
 * @returns {Promise<Array>} Calendar events
 */
export const getCalendarEvents = async (userId, userRole, dateRange = {}) => {
  try {
    let query = supabase
      .from('interviews')
      .select(`
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          profile_photo_url
        ),
        jobs:job_id (
          id,
          title
        ),
        company_profiles:company_id (
          company_name,
          company_logo_url
        ),
        interviewer_profiles:interviewer_id (
          full_name,
          email,
          profile_photo_url,
          current_designation
        )
      `);

    // Filter by user role
    switch (userRole) {
      case 'candidate':
        query = query.eq('candidate_id', userId);
        break;
      case 'interviewer':
        query = query.eq('interviewer_id', userId);
        break;
      case 'company':
        query = query.eq('company_id', userId);
        break;
    }

    // Filter by date range if provided
    if (dateRange.start) {
      query = query.gte('interview_date', dateRange.start);
    }
    if (dateRange.end) {
      query = query.lte('interview_date', dateRange.end);
    }

    // Only include interviews with dates
    query = query.not('interview_date', 'is', null);
    
    // Order by date
    query = query.order('interview_date', { ascending: true });

    const { data, error } = await query;

    if (error) throw error;

    // Convert interviews to calendar events
    const events = (data || []).map(interview => 
      interviewToCalendarEvent(interview, userRole)
    );

    return { success: true, data: events };
  } catch (error) {
    console.error('Error fetching calendar events:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Generate Google Calendar URL for an interview
 * @param {Object} interview - Interview object
 * @param {string} userRole - User role
 * @returns {string} Google Calendar URL
 */
export const generateGoogleCalendarUrl = (interview, userRole = 'candidate') => {
  const event = interviewToCalendarEvent(interview, userRole);
  const startDate = dayjs(event.start).format('YYYYMMDDTHHmmss');
  const endDate = dayjs(event.end).format('YYYYMMDDTHHmmss');

  const params = new URLSearchParams({
    action: 'TEMPLATE',
    text: event.title,
    dates: `${startDate}/${endDate}`,
    details: event.description,
    location: event.meeting_link || 'Video Call',
  });

  return `https://calendar.google.com/calendar/render?${params.toString()}`;
};

/**
 * Generate Outlook Calendar URL for an interview
 * @param {Object} interview - Interview object
 * @param {string} userRole - User role
 * @returns {string} Outlook Calendar URL
 */
export const generateOutlookCalendarUrl = (interview, userRole = 'candidate') => {
  const event = interviewToCalendarEvent(interview, userRole);
  const startDate = dayjs(event.start).toISOString();
  const endDate = dayjs(event.end).toISOString();

  const params = new URLSearchParams({
    subject: event.title,
    startdt: startDate,
    enddt: endDate,
    body: event.description,
    location: event.meeting_link || 'Video Call',
  });

  return `https://outlook.live.com/calendar/0/deeplink/compose?${params.toString()}`;
};

/**
 * Generate ICS file content for an interview
 * @param {Object} interview - Interview object
 * @param {string} userRole - User role
 * @returns {string} ICS file content
 */
export const generateICSFile = (interview, userRole = 'candidate') => {
  const event = interviewToCalendarEvent(interview, userRole);
  const startDate = dayjs(event.start).utc().format('YYYYMMDDTHHmmss[Z]');
  const endDate = dayjs(event.end).utc().format('YYYYMMDDTHHmmss[Z]');
  const now = dayjs().utc().format('YYYYMMDDTHHmmss[Z]');

  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Flyt//Interview Calendar//EN',
    'BEGIN:VEVENT',
    `UID:interview-${interview.id}@flyt.com`,
    `DTSTAMP:${now}`,
    `DTSTART:${startDate}`,
    `DTEND:${endDate}`,
    `SUMMARY:${event.title}`,
    `DESCRIPTION:${event.description.replace(/\n/g, '\\n')}`,
    `LOCATION:${event.meeting_link || 'Video Call'}`,
    'STATUS:CONFIRMED',
    'END:VEVENT',
    'END:VCALENDAR',
  ].join('\r\n');

  return icsContent;
};

/**
 * Download ICS file for an interview
 * @param {Object} interview - Interview object
 * @param {string} userRole - User role
 */
export const downloadICSFile = (interview, userRole = 'candidate') => {
  const icsContent = generateICSFile(interview, userRole);
  const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `interview-${interview.id}.ics`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

export default {
  interviewToCalendarEvent,
  getCalendarEvents,
  generateGoogleCalendarUrl,
  generateOutlookCalendarUrl,
  generateICSFile,
  downloadICSFile,
};
