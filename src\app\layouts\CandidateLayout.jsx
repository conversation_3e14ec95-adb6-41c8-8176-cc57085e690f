import React, { useState, useEffect } from 'react';
import { Layout, Skeleton, Spin } from 'antd';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { User, Settings, LogOut, Sun, Moon } from 'lucide-react';
import useDeviceDetect from '@/hooks/useDeviceDetect';
import { useColorModeStore } from '@/store/colorMode.store';
import useAuth from '@/hooks/useAuth';
import useLogout from '@/hooks/useLogout';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import { logo_lite, logo_dark } from '@/assets';
import SearchModal from '@/components/shared/SearchModal';
import showToast from '@/utils/toast';
import PageTitle from '@/components/PageTitle';
import FloatingThemeToggle from '@/components/shared/FloatingThemeToggle';

// Import custom layout components
import CandidateHeader from '@/features/candidate/components/layout/CandidateHeader';
import CandidateFooter from '@/features/candidate/components/layout/CandidateFooter';
import CandidateMobileDrawer from '@/features/candidate/components/layout/CandidateMobileDrawer';

const { Content } = Layout;

const CandidateLayout = () => {
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [notifications, setNotifications] = useState([]);

  const location = useLocation();
  const { isMobile } = useDeviceDetect();
  const { colorMode, toggleColorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Auth state from useAuth hook
  const { user } = useAuth();

  // Logout functionality with loading state
  const { handleLogout, isLoggingOut } = useLogout();

  // Candidate state from candidate store
  const candidateStore = useCandidateStore();
  const { profile } = candidateStore || {};

  // Initialize notifications
  useEffect(() => {
    setNotifications([
      { id: 1, title: 'New job match found', read: false },
      { id: 2, title: 'Interview scheduled', read: false },
      { id: 3, title: 'Application status updated', read: true },
    ]);
  }, []);

  const handleThemeToggle = () => {
    toggleColorMode();
    showToast.success(`Switched to ${isDark ? 'light' : 'dark'} mode`);
  };

  // User menu items
  const userMenuItems = [
    {
      key: 'profile',
      label: <Link to="/candidate/profile">My Profile</Link>,
      icon: <User size={16} />,
    },
    {
      key: 'settings',
      label: <Link to="/candidate/settings">Settings</Link>,
      icon: <Settings size={16} />,
    },
    {
      key: 'theme',
      label: <div onClick={handleThemeToggle}>{isDark ? 'Light Mode' : 'Dark Mode'}</div>,
      icon: isDark ? <Sun size={16} /> : <Moon size={16} />,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: <div onClick={handleLogout}>{isLoggingOut ? <Spin size="small" /> : 'Logout'}</div>,
      icon: <LogOut size={16} />,
    },
  ];

  // Notification menu items
  const notificationItems = notifications.map((notification) => ({
    key: notification.id,
    label: (
      <div className={`notification-item ${!notification.read ? 'unread' : ''}`}>
        <div className="notification-title">{notification.title}</div>
      </div>
    ),
  }));

  // Get current page from URL
  const currentPage = location.pathname.split('/')[2] || 'dashboard';

  return (
    <Layout className="min-h-screen transition-colors duration-200">
      <PageTitle
        title="Candidate Portal"
        description="Find and apply for jobs, manage applications and interviews"
      />

      {/* Header Component */}
      <CandidateHeader
        user={user}
        profile={profile}
        notifications={notifications}
        setMobileDrawerOpen={setMobileDrawerOpen}
        setSearchModalVisible={setSearchModalVisible}
        userMenuItems={userMenuItems}
        notificationItems={notificationItems}
        isDark={isDark}
        logo_dark={logo_dark}
        logo_lite={logo_lite}
      />

      {/* Mobile Drawer Component */}
      <CandidateMobileDrawer
        open={mobileDrawerOpen}
        onClose={() => setMobileDrawerOpen(false)}
        handleLogout={handleLogout}
        currentPage={currentPage}
        isDark={isDark}
        logo_dark={logo_dark}
        logo_lite={logo_lite}
      />

      {/* Main Content */}
      <Content className="flex-1 transition-colors duration-200 p-4 sm:p-6 lg:p-8 min-h-0">
        <div className="max-w-7xl mx-auto w-full">
          <Outlet />
        </div>
      </Content>

      {/* Floating theme toggle for mobile */}
      {isMobile && <FloatingThemeToggle position="bottom-right" />}

      {/* Footer Component */}
      <CandidateFooter />

      {/* Search Modal */}
      <SearchModal
        visible={searchModalVisible}
        onClose={() => setSearchModalVisible(false)}
      />
    </Layout>
  );
};

export default CandidateLayout;
