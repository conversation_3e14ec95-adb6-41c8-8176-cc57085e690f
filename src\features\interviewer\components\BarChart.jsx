import React from 'react';
import { Card, Typography, Select, Spin } from 'antd';
import { Column } from '@ant-design/plots';
import { useColorModeStore } from '@/store/colorMode.store';

const { Title } = Typography;
const { Option } = Select;

const BarChart = ({
  title,
  data,
  loading = false,
  timeRange = 'This Month',
  onTimeRangeChange,
  height = 300,
}) => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const config = {
    data,
    isStack: true,
    xField: 'month',
    yField: 'value',
    seriesField: 'type',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    color: ['#1890FF', '#40A9FF'],
    columnStyle: {
      radius: [8, 8, 0, 0],
    },
    xAxis: {
      label: {
        style: {
          fill: isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.65)',
        },
      },
      line: {
        style: {
          stroke: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
        },
      },
    },
    yAxis: {
      label: {
        style: {
          fill: isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.65)',
        },
      },
      grid: {
        line: {
          style: {
            stroke: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
            lineDash: [4, 4],
          },
        },
      },
    },
    legend: {
      position: 'top-right',
      itemName: {
        style: {
          fill: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        },
      },
    },
    tooltip: {
      customContent: (title, items) => {
        return (
          <div className="p-2">
            <h5 className="m-0 mb-2">{title}</h5>
            <ul
              className="p-0 m-0"
              style={{ listStyle: 'none' }}
            >
              {items.map((item, index) => (
                <li
                  key={index}
                  className="flex items-center"
                >
                  <span
                    className="inline-block mr-2 rounded-full"
                    style={{
                      width: '8px',
                      height: '8px',
                      backgroundColor: item.color,
                    }}
                  />
                  <span>{item.name}: </span>
                  <span className="ml-1 font-bold">{item.value}</span>
                </li>
              ))}
            </ul>
          </div>
        );
      },
    },
  };

  return (
    <Card
      className="chart-card h-full transition-all hover:shadow-md"
      style={{
        borderRadius: '12px',
        borderColor: 'transparent',
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.04)' : 'white',
      }}
      styles={{ body: { padding: '16px' } }}
    >
      <div className="flex items-center justify-between mb-4">
        <Title
          level={5}
          className="m-0"
        >
          {title}
        </Title>
        <Select
          defaultValue={timeRange}
          style={{ width: 120 }}
          onChange={onTimeRangeChange}
          size="small"
        >
          <Option value="This Week">This Week</Option>
          <Option value="This Month">This Month</Option>
          <Option value="This Quarter">This Quarter</Option>
          <Option value="This Year">This Year</Option>
        </Select>
      </div>

      <div style={{ height: `${height}px`, position: 'relative' }}>
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <Spin />
          </div>
        ) : (
          <Column {...config} />
        )}
      </div>
    </Card>
  );
};

export default BarChart;
