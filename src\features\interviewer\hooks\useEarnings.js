import { useEffect, useState } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';

const useEarnings = () => {
  const [earnings, setEarnings] = useState([]);
  const [totalEarned, setTotalEarned] = useState(0);
  const [pendingAmount, setPendingAmount] = useState(0);
  const [loading, setLoading] = useState(false);
  const { user, profile } = useAuth();

  const fetchEarnings = async () => {
    if (!user || !profile) return;

    setLoading(true);
    try {
      // Fetch all payments for this interviewer
      const { data, error } = await supabase
        .from('interviewer_payments')
        .select('*')
        .eq('interviewer_id', profile.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setEarnings(data);

      // Calculate total earned
      const total = data.reduce((sum, payment) => {
        return payment.status === 'paid' ? sum + payment.amount : sum;
      }, 0);
      setTotalEarned(total);

      // Calculate pending amount
      const pending = data.reduce((sum, payment) => {
        return payment.status === 'pending' ? sum + payment.amount : sum;
      }, 0);
      setPendingAmount(pending);

      return data;
    } catch (error) {
      console.error('Failed to fetch earnings', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user && profile) {
      fetchEarnings();
    }
  }, [user, profile]);

  return {
    earnings,
    totalEarned,
    pendingAmount,
    loading,
    refetch: fetchEarnings,
  };
};

export default useEarnings;
