/**
 * Candidate Data Service
 * 
 * Handles candidate-specific data operations and API calls
 * Extracted from shared dataFetchService for better organization
 */

import { supabase } from '@/utils/supabaseClient';
import cacheService, { CACHE_TTL, CACHE_KEYS } from '@/utils/cacheService';

/**
 * Candidate Data Service for optimized data fetching and caching
 */
const candidateDataService = {
  /**
   * Fetch candidate profile with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Candidate profile
   */
  async fetchCandidateProfile(userId, forceRefresh = false) {
    if (!userId) return null;
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.PROFILE, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedProfile = cacheService.get(cacheKey);
      if (cachedProfile) return cachedProfile;
    }
    
    try {
      // Fetch the complete candidate profile view
      const { data, error } = await supabase
        .from('candidate_profiles_complete')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.PROFILE);
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching candidate profile:', error);
      throw error;
    }
  },
  
  /**
   * Fetch applied jobs with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Applied jobs
   */
  async fetchAppliedJobs(userId, forceRefresh = false) {
    if (!userId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.APPLICATIONS, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Fetch applied jobs with optimized select
      const { data, error } = await supabase
        .from('applications')
        .select(`
          id,
          status,
          application_date,
          created_at,
          jobs:job_id (
            id,
            title,
            location,
            experience_level,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('candidate_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.APPLICATIONS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching applied jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch saved jobs with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Saved jobs
   */
  async fetchSavedJobs(userId, forceRefresh = false) {
    if (!userId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.SAVED_JOBS, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Fetch saved jobs with optimized select
      const { data, error } = await supabase
        .from('saved_jobs')
        .select(`
          id,
          created_at,
          jobs:job_id (
            id,
            title,
            location,
            experience_level,
            salary_range,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('candidate_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.JOBS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching saved jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch upcoming interviews with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Upcoming interviews
   */
  async fetchUpcomingInterviews(userId, forceRefresh = false) {
    if (!userId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedInterviews = cacheService.get(cacheKey);
      if (cachedInterviews) return cachedInterviews;
    }
    
    try {
      // Get current date in ISO format
      const now = new Date().toISOString();
      
      // Fetch upcoming interviews
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          id,
          interview_date,
          duration_minutes,
          status,
          interviewer:interviewer_id (
            id,
            full_name
          ),
          job:job_id (
            id,
            title,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('candidate_id', userId)
        .gte('interview_date', now)
        .order('interview_date', { ascending: true });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        // Shorter TTL for interviews as they're time-sensitive
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching upcoming interviews:', error);
      return [];
    }
  },
  
  /**
   * Apply to a job with cache invalidation
   * @param {string} userId - User ID
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} - Application data
   */
  async applyToJob(userId, jobId) {
    if (!userId || !jobId) {
      throw new Error('User ID and Job ID are required');
    }
    
    try {
      // Create application
      const { data, error } = await supabase
        .from('applications')
        .insert({
          candidate_id: userId,
          job_id: jobId,
          status: 'applied',
          application_date: new Date().toISOString(),
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Invalidate relevant caches
      this.clearCache(userId, 'APPLICATIONS');
      
      return data;
    } catch (error) {
      console.error('Error applying to job:', error);
      throw error;
    }
  },
  
  /**
   * Save or unsave a job with cache invalidation
   * @param {string} userId - User ID
   * @param {string} jobId - Job ID
   * @param {boolean} save - Whether to save or unsave
   * @returns {Promise<Object>} - Result
   */
  async toggleSaveJob(userId, jobId, save = true) {
    if (!userId || !jobId) {
      throw new Error('User ID and Job ID are required');
    }
    
    try {
      let result;
      
      if (save) {
        // Save job
        const { data, error } = await supabase
          .from('saved_jobs')
          .insert({
            candidate_id: userId,
            job_id: jobId,
          })
          .select()
          .single();
          
        if (error) throw error;
        result = data;
      } else {
        // Unsave job
        const { data, error } = await supabase
          .from('saved_jobs')
          .delete()
          .match({ candidate_id: userId, job_id: jobId })
          .select()
          .single();
          
        if (error) throw error;
        result = data;
      }
      
      // Invalidate saved jobs cache
      this.clearCache(userId, 'SAVED_JOBS');
      
      return result;
    } catch (error) {
      console.error(`Error ${save ? 'saving' : 'unsaving'} job:`, error);
      throw error;
    }
  },
  
  /**
   * Clear cache for a specific candidate
   * @param {string} userId - User ID
   * @param {string} cacheType - Type of cache to clear (or all if not specified)
   */
  clearCache(userId, cacheType = null) {
    if (!userId) return;
    
    if (cacheType) {
      // Clear specific cache type
      const cacheKey = cacheService.getCacheKey(CACHE_KEYS[cacheType], userId);
      cacheService.del(cacheKey);
    } else {
      // Clear all cache for this user
      Object.values(CACHE_KEYS).forEach(prefix => {
        const cacheKey = cacheService.getCacheKey(prefix, userId);
        cacheService.del(cacheKey);
      });
    }
  }
};

export default candidateDataService;
