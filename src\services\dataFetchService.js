/**
 * Data Fetch Service
 * 
 * Centralized data fetching service with browser storage caching
 * Optimizes data loading for candidate dashboard, applications, and jobs
 */

import { supabase } from '@/utils/supabaseClient';
import cacheService, { CACHE_TTL, CACHE_KEYS } from '@/utils/cacheService';

// Additional cache keys for company and interviewer
const COMPANY_CACHE_KEYS = {
  PROFILE: 'company_profile',
  JOBS: 'company_jobs',
  APPLICATIONS: 'company_applications',
  INTERVIEWS: 'company_interviews',
  CANDIDATES: 'company_candidates',
};

const INTERVIEWER_CACHE_KEYS = {
  PROFILE: 'interviewer_profile',
  INTERVIEWS: 'interviewer_interviews',
  ASSESSMENTS: 'interviewer_assessments',
  CANDIDATES: 'interviewer_candidates',
};

/**
 * Data Service for optimized data fetching and caching
 * Supports all roles: candidate, company, and interviewer
 */
const dataFetchService = {
  /**
   * Fetch candidate profile with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Candidate profile
   */
  async fetchCandidateProfile(userId, forceRefresh = false) {
    if (!userId) return null;
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.PROFILE, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedProfile = cacheService.get(cacheKey);
      if (cachedProfile) return cachedProfile;
    }
    
    try {
      // Fetch the complete candidate profile view
      const { data, error } = await supabase
        .from('candidate_profiles_complete')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.PROFILE);
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching candidate profile:', error);
      throw error;
    }
  },
  
  /**
   * Fetch applied jobs with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Applied jobs
   */
  async fetchAppliedJobs(userId, forceRefresh = false) {
    if (!userId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.APPLICATIONS, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Fetch applied jobs with optimized select
      const { data, error } = await supabase
        .from('applications')
        .select(`
          id,
          status,
          application_date,
          created_at,
          jobs:job_id (
            id,
            title,
            location,
            experience_level,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('candidate_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.APPLICATIONS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching applied jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch saved jobs with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Saved jobs
   */
  async fetchSavedJobs(userId, forceRefresh = false) {
    if (!userId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.SAVED_JOBS, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Fetch saved jobs with optimized select
      const { data, error } = await supabase
        .from('saved_jobs')
        .select(`
          id,
          created_at,
          jobs:job_id (
            id,
            title,
            location,
            experience_level,
            salary_range,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('candidate_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.JOBS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching saved jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch recommended jobs with caching
   * @param {string} userId - User ID
   * @param {Object} filters - Job filters
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Recommended jobs
   */
  async fetchRecommendedJobs(userId, filters = {}, forceRefresh = false) {
    if (!userId) return [];
    
    // Create a unique cache key based on filters
    const filterKey = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}:${value}`)
      .join('_');
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.JOBS, userId, `recommended_${filterKey}`);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Start building the query
      let query = supabase
        .from('jobs')
        .select(`
          id,
          title,
          location,
          experience_level,
          salary_range,
          created_at,
          companies:company_id (
            id,
            company_name,
            company_logo_url
          )
        `)
        .eq('status', 'active');
      
      // Apply filters
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }
      
      if (filters.experienceLevel) {
        query = query.eq('experience_level', filters.experienceLevel);
      }
      
      if (filters.skills && filters.skills.length > 0) {
        // This is a simplified approach - in a real app, you'd want a more sophisticated skill matching algorithm
        const skillsCondition = filters.skills.map(skill => 
          `required_skills::jsonb @> '["${skill}"]'::jsonb`
        ).join(' OR ');
        
        query = query.or(skillsCondition);
      }
      
      // Execute the query
      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(20);
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.JOBS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching recommended jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch upcoming interviews with caching
   * @param {string} userId - User ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Upcoming interviews
   */
  async fetchUpcomingInterviews(userId, forceRefresh = false) {
    if (!userId) return [];
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.INTERVIEWS, userId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedInterviews = cacheService.get(cacheKey);
      if (cachedInterviews) return cachedInterviews;
    }
    
    try {
      // Get current date in ISO format
      const now = new Date().toISOString();
      
      // Fetch upcoming interviews
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          id,
          interview_date,
          duration_minutes,
          status,
          interviewer:interviewer_id (
            id,
            full_name
          ),
          job:job_id (
            id,
            title,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('candidate_id', userId)
        .gte('interview_date', now)
        .order('interview_date', { ascending: true });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        // Shorter TTL for interviews as they're time-sensitive
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching upcoming interviews:', error);
      return [];
    }
  },
  
  /**
   * Apply to a job with cache invalidation
   * @param {string} userId - User ID
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} - Application data
   */
  async applyToJob(userId, jobId) {
    if (!userId || !jobId) {
      throw new Error('User ID and Job ID are required');
    }
    
    try {
      // Create application
      const { data, error } = await supabase
        .from('applications')
        .insert({
          candidate_id: userId,
          job_id: jobId,
          status: 'applied',
          application_date: new Date().toISOString(),
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Invalidate relevant caches
      this.clearCache(userId, 'APPLICATIONS');
      
      return data;
    } catch (error) {
      console.error('Error applying to job:', error);
      throw error;
    }
  },
  
  /**
   * Save or unsave a job with cache invalidation
   * @param {string} userId - User ID
   * @param {string} jobId - Job ID
   * @param {boolean} save - Whether to save or unsave
   * @returns {Promise<Object>} - Result
   */
  async toggleSaveJob(userId, jobId, save = true) {
    if (!userId || !jobId) {
      throw new Error('User ID and Job ID are required');
    }
    
    try {
      let result;
      
      if (save) {
        // Save job
        const { data, error } = await supabase
          .from('saved_jobs')
          .insert({
            candidate_id: userId,
            job_id: jobId,
          })
          .select()
          .single();
          
        if (error) throw error;
        result = data;
      } else {
        // Unsave job
        const { data, error } = await supabase
          .from('saved_jobs')
          .delete()
          .match({ candidate_id: userId, job_id: jobId })
          .select()
          .single();
          
        if (error) throw error;
        result = data;
      }
      
      // Invalidate saved jobs cache
      this.clearCache(userId, 'SAVED_JOBS');
      
      return result;
    } catch (error) {
      console.error(`Error ${save ? 'saving' : 'unsaving'} job:`, error);
      throw error;
    }
  },
  
  /**
   * Clear cache for a specific user
   * @param {string} userId - User ID
   * @param {string} cacheType - Type of cache to clear (or all if not specified)
   * @param {string} role - User role (candidate, company, interviewer)
   */
  clearCache(userId, cacheType = null, role = 'candidate') {
    if (!userId) return;
    
    // Select the appropriate cache keys based on role
    const keysMap = {
      candidate: CACHE_KEYS,
      company: COMPANY_CACHE_KEYS,
      interviewer: INTERVIEWER_CACHE_KEYS
    };
    
    const keys = keysMap[role] || CACHE_KEYS;
    
    if (cacheType) {
      // Clear specific cache type
      const cacheKey = cacheService.getCacheKey(keys[cacheType], userId);
      cacheService.del(cacheKey);
    } else {
      // Clear all cache for this user
      Object.values(keys).forEach(prefix => {
        const cacheKey = cacheService.getCacheKey(prefix, userId);
        cacheService.del(cacheKey);
      });
    }
  },

  // === COMPANY ROLE METHODS ===
  
  /**
   * Fetch company profile with caching
   * @param {string} companyId - Company ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Company profile
   */
  async fetchCompanyProfile(companyId, forceRefresh = false) {
    if (!companyId) return null;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.PROFILE, companyId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedProfile = cacheService.get(cacheKey);
      if (cachedProfile) return cachedProfile;
    }
    
    try {
      // Fetch the complete company profile view
      const { data, error } = await supabase
        .from('company_profiles_complete')
        .select('*')
        .eq('id', companyId)
        .single();
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.PROFILE);
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching company profile:', error);
      throw error;
    }
  },
  
  /**
   * Fetch company jobs with caching
   * @param {string} companyId - Company ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Company jobs
   */
  async fetchCompanyJobs(companyId, forceRefresh = false) {
    if (!companyId) return [];
    
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.JOBS, companyId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Fetch company jobs with optimized select
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          id,
          title,
          description,
          location,
          experience_level,
          salary_range,
          required_skills,
          status,
          created_at,
          updated_at
        `)
        .eq('company_id', companyId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.JOBS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching company jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch company job applications with caching
   * @param {string} companyId - Company ID
   * @param {string} jobId - Optional job ID to filter by
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Job applications
   */
  async fetchCompanyApplications(companyId, jobId = null, forceRefresh = false) {
    if (!companyId) return [];
    
    const cacheKeySuffix = jobId ? `_job_${jobId}` : '';
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.APPLICATIONS, companyId, cacheKeySuffix);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedApplications = cacheService.get(cacheKey);
      if (cachedApplications) return cachedApplications;
    }
    
    try {
      // Start building the query
      let query = supabase
        .from('applications')
        .select(`
          id,
          status,
          application_date,
          created_at,
          updated_at,
          jobs:job_id (id, title),
          candidates:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (resume_url, years_experience, current_job_title)
          )
        `)
        .eq('jobs.company_id', companyId);
      
      // Add job filter if specified
      if (jobId) {
        query = query.eq('job_id', jobId);
      }
      
      // Execute the query
      const { data, error } = await query
        .order('application_date', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.APPLICATIONS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching company applications:', error);
      return [];
    }
  },
  
  // === INTERVIEWER ROLE METHODS ===
  
  /**
   * Fetch interviewer profile with caching
   * @param {string} interviewerId - Interviewer ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Interviewer profile
   */
  async fetchInterviewerProfile(interviewerId, forceRefresh = false) {
    if (!interviewerId) return null;
    
    const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS.PROFILE, interviewerId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedProfile = cacheService.get(cacheKey);
      if (cachedProfile) return cachedProfile;
    }
    
    try {
      // Fetch the complete interviewer profile view
      const { data, error } = await supabase
        .from('interviewer_profiles_complete')
        .select('*')
        .eq('id', interviewerId)
        .single();
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.PROFILE);
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching interviewer profile:', error);
      throw error;
    }
  },
  
  /**
   * Fetch interviewer's scheduled interviews with caching
   * @param {string} interviewerId - Interviewer ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Scheduled interviews
   */
  async fetchInterviewerSchedule(interviewerId, forceRefresh = false) {
    if (!interviewerId) return [];
    
    const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS.INTERVIEWS, interviewerId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedInterviews = cacheService.get(cacheKey);
      if (cachedInterviews) return cachedInterviews;
    }
    
    try {
      // Get current date in ISO format
      const now = new Date().toISOString();
      
      // Fetch upcoming interviews
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          id,
          interview_date,
          duration_minutes,
          status,
          feedback,
          score,
          candidate:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (resume_url, years_experience, current_job_title)
          ),
          job:job_id (
            id,
            title,
            companies:company_id (
              id,
              company_name
            )
          )
        `)
        .eq('interviewer_id', interviewerId)
        .gte('interview_date', now)
        .order('interview_date', { ascending: true });
      
      if (error) throw error;
      
      // Cache the result with shorter TTL for time-sensitive data
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching interviewer schedule:', error);
      return [];
    }
  }
};

export default dataFetchService;
