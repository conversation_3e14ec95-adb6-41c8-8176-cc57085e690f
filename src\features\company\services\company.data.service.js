/**
 * Company Data Service
 * 
 * Handles company-specific data operations and API calls
 * Extracted from shared dataFetchService for better organization
 */

import { supabase } from '@/utils/supabaseClient';
import cacheService, { CACHE_TTL } from '@/utils/cacheService';

// Company-specific cache keys
const COMPANY_CACHE_KEYS = {
  PROFILE: 'company_profile',
  JOBS: 'company_jobs',
  APPLICATIONS: 'company_applications',
  INTERVIEWS: 'company_interviews',
  STATS: 'company_stats',
};

/**
 * Company Data Service for optimized data fetching and caching
 */
const companyDataService = {
  /**
   * Fetch company profile with caching
   * @param {string} companyId - Company ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Company profile
   */
  async fetchCompanyProfile(companyId, forceRefresh = false) {
    if (!companyId) return null;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.PROFILE, companyId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedProfile = cacheService.get(cacheKey);
      if (cachedProfile) return cachedProfile;
    }
    
    try {
      // Fetch the complete company profile view
      const { data, error } = await supabase
        .from('company_profiles_complete')
        .select('*')
        .eq('id', companyId)
        .single();
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.PROFILE);
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching company profile:', error);
      throw error;
    }
  },
  
  /**
   * Fetch company jobs with caching
   * @param {string} companyId - Company ID
   * @param {Object} filters - Optional filters
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Company jobs
   */
  async fetchCompanyJobs(companyId, filters = {}, forceRefresh = false) {
    if (!companyId) return [];
    
    // Create cache key with filters
    const filterKey = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}:${value}`)
      .join('_');
    
    const cacheKey = cacheService.getCacheKey(
      COMPANY_CACHE_KEYS.JOBS, 
      companyId, 
      filterKey || 'all'
    );
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      let query = supabase
        .from('jobs')
        .select(`
          id,
          title,
          description,
          location,
          experience_level,
          salary_range,
          required_skills,
          status,
          created_at,
          updated_at,
          applications_count:applications(count)
        `)
        .eq('company_id', companyId);

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters.experience_level) {
        query = query.eq('experience_level', filters.experience_level);
      }
      
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }
      
      const { data, error } = await query
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.JOBS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching company jobs:', error);
      return [];
    }
  },
  
  /**
   * Fetch company job applications with caching
   * @param {string} companyId - Company ID
   * @param {string} jobId - Optional job ID to filter by
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Job applications
   */
  async fetchCompanyApplications(companyId, jobId = null, forceRefresh = false) {
    if (!companyId) return [];
    
    const cacheKeySuffix = jobId ? `_job_${jobId}` : '';
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.APPLICATIONS, companyId, cacheKeySuffix);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedApplications = cacheService.get(cacheKey);
      if (cachedApplications) return cachedApplications;
    }
    
    try {
      // Start building the query
      let query = supabase
        .from('applications')
        .select(`
          id,
          status,
          application_date,
          interview_score,
          created_at,
          updated_at,
          jobs:job_id (id, title),
          candidates:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (
              resume_url, 
              years_experience, 
              current_job_title,
              profile_photo_url,
              skills,
              expected_ctc
            )
          )
        `);

      // Filter by company through jobs
      if (jobId) {
        query = query.eq('job_id', jobId);
      } else {
        // Get all applications for company jobs
        const { data: companyJobs } = await supabase
          .from('jobs')
          .select('id')
          .eq('company_id', companyId);
        
        if (companyJobs && companyJobs.length > 0) {
          const jobIds = companyJobs.map(job => job.id);
          query = query.in('job_id', jobIds);
        } else {
          return []; // No jobs, no applications
        }
      }
      
      // Execute the query
      const { data, error } = await query
        .order('application_date', { ascending: false });
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.APPLICATIONS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching company applications:', error);
      return [];
    }
  },

  /**
   * Fetch company interview statistics
   * @param {string} companyId - Company ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Interview statistics
   */
  async fetchCompanyInterviewStats(companyId, forceRefresh = false) {
    if (!companyId) return null;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.STATS, companyId, 'interviews');
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedStats = cacheService.get(cacheKey);
      if (cachedStats) return cachedStats;
    }
    
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select('status, score')
        .eq('company_id', companyId);

      if (error) throw error;

      const stats = {
        total: data.length,
        requested: data.filter(i => i.status === 'requested').length,
        scheduled: data.filter(i => i.status === 'scheduled').length,
        completed: data.filter(i => i.status === 'completed').length,
        cancelled: data.filter(i => i.status === 'cancelled').length,
        averageScore: 0,
      };

      // Calculate average score for completed interviews
      const completedWithScores = data.filter(i => i.status === 'completed' && i.score);
      if (completedWithScores.length > 0) {
        const totalScore = completedWithScores.reduce((sum, i) => sum + i.score, 0);
        stats.averageScore = Math.round((totalScore / completedWithScores.length) * 10) / 10;
      }

      // Cache the result
      cacheService.set(cacheKey, stats, CACHE_TTL.STATS);

      return stats;
    } catch (error) {
      console.error('Error fetching company interview stats:', error);
      return null;
    }
  },

  /**
   * Fetch company dashboard statistics
   * @param {string} companyId - Company ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Dashboard statistics
   */
  async fetchCompanyDashboardStats(companyId, forceRefresh = false) {
    if (!companyId) return null;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS.STATS, companyId, 'dashboard');
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedStats = cacheService.get(cacheKey);
      if (cachedStats) return cachedStats;
    }
    
    try {
      // Get job statistics
      const { data: jobs, error: jobsError } = await supabase
        .from('jobs')
        .select('id, status')
        .eq('company_id', companyId);

      if (jobsError) throw jobsError;

      const jobIds = jobs.map(job => job.id);
      
      // Get application statistics
      const { data: applications, error: appsError } = await supabase
        .from('applications')
        .select('status')
        .in('job_id', jobIds);

      if (appsError) throw appsError;

      // Get interview statistics
      const { data: interviews, error: interviewsError } = await supabase
        .from('interviews')
        .select('status')
        .eq('company_id', companyId);

      if (interviewsError) throw interviewsError;

      const stats = {
        jobs: {
          total: jobs.length,
          active: jobs.filter(j => j.status === 'active').length,
          draft: jobs.filter(j => j.status === 'draft').length,
          closed: jobs.filter(j => j.status === 'closed').length,
        },
        applications: {
          total: applications.length,
          pending: applications.filter(a => a.status === 'applied').length,
          reviewed: applications.filter(a => a.status === 'reviewed').length,
          interviewed: applications.filter(a => a.status === 'interviewed').length,
          hired: applications.filter(a => a.status === 'hired').length,
          rejected: applications.filter(a => a.status === 'rejected').length,
        },
        interviews: {
          total: interviews.length,
          requested: interviews.filter(i => i.status === 'requested').length,
          scheduled: interviews.filter(i => i.status === 'scheduled').length,
          completed: interviews.filter(i => i.status === 'completed').length,
        }
      };

      // Cache the result
      cacheService.set(cacheKey, stats, CACHE_TTL.STATS);

      return stats;
    } catch (error) {
      console.error('Error fetching company dashboard stats:', error);
      return null;
    }
  },

  /**
   * Clear cache for a specific company
   * @param {string} companyId - Company ID
   * @param {string} cacheType - Type of cache to clear (or all if not specified)
   */
  clearCache(companyId, cacheType = null) {
    if (!companyId) return;
    
    if (cacheType) {
      // Clear specific cache type
      const cacheKey = cacheService.getCacheKey(COMPANY_CACHE_KEYS[cacheType], companyId);
      cacheService.del(cacheKey);
    } else {
      // Clear all cache for this company
      Object.values(COMPANY_CACHE_KEYS).forEach(prefix => {
        const cacheKey = cacheService.getCacheKey(prefix, companyId);
        cacheService.del(cacheKey);
      });
    }
  }
};

export default companyDataService;
