/**
 * Candidate Job Service
 * 
 * Handles candidate-specific job operations and API calls
 * Includes job browsing, searching, and application management
 */

import { supabase } from '@/utils/supabaseClient';
import cacheService, { CACHE_TTL, CACHE_KEYS } from '@/utils/cacheService';

/**
 * Candidate Job Service for job-related operations
 */
const candidateJobService = {
  /**
   * Get a job by ID with candidate-specific data
   * @param {string} id - Job ID
   * @param {string} candidateId - Candidate ID for checking application status
   * @returns {Promise<Object>} - Job data with application status
   */
  async getJob(id, candidateId = null) {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select(
          `
          *,
          companies:company_id (
            id,
            company_name,
            company_logo_url,
            company_type,
            company_size,
            company_description,
            website_url,
            location as company_location
          )
        `
        )
        .eq('id', id)
        .single();

      if (error) throw error;

      // Check if candidate has applied to this job
      if (candidateId && data) {
        const { data: applicationData } = await supabase
          .from('applications')
          .select('id, status, application_date')
          .eq('job_id', id)
          .eq('candidate_id', candidateId)
          .single();

        data.application = applicationData || null;
        data.isApplied = !!applicationData;
      }

      // Check if candidate has saved this job
      if (candidateId && data) {
        const { data: savedData } = await supabase
          .from('saved_jobs')
          .select('id, created_at')
          .eq('job_id', id)
          .eq('candidate_id', candidateId)
          .single();

        data.isSaved = !!savedData;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching job:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get all active jobs with candidate-specific filtering
   * @param {Object} filters - Optional filters
   * @param {string} candidateId - Candidate ID for personalization
   * @returns {Promise<Object>} - Active jobs
   */
  async getActiveJobs(filters = {}, candidateId = null) {
    try {
      let query = supabase
        .from('jobs')
        .select(
          `
          *,
          companies:company_id (
            id,
            company_name,
            company_logo_url,
            company_type,
            company_size
          )
        `
        )
        .eq('status', 'active');

      // Apply filters if provided
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }

      if (filters.experienceLevel) {
        query = query.eq('experience_level', filters.experienceLevel);
      }

      if (filters.title) {
        query = query.ilike('title', `%${filters.title}%`);
      }

      if (filters.companyType) {
        query = query.eq('companies.company_type', filters.companyType);
      }

      if (filters.salaryMin) {
        query = query.gte('salary_min', filters.salaryMin);
      }

      if (filters.salaryMax) {
        query = query.lte('salary_max', filters.salaryMax);
      }

      // Add skills filter if provided
      if (filters.skills && filters.skills.length > 0) {
        const skillsCondition = filters.skills.map(skill => 
          `required_skills::jsonb @> '["${skill}"]'::jsonb`
        ).join(' OR ');
        
        query = query.or(skillsCondition);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(filters.limit || 50);

      if (error) throw error;

      // Add application and saved status for each job if candidateId provided
      if (candidateId && data) {
        const jobIds = data.map(job => job.id);
        
        // Get applications
        const { data: applications } = await supabase
          .from('applications')
          .select('job_id, status, application_date')
          .eq('candidate_id', candidateId)
          .in('job_id', jobIds);

        // Get saved jobs
        const { data: savedJobs } = await supabase
          .from('saved_jobs')
          .select('job_id, created_at')
          .eq('candidate_id', candidateId)
          .in('job_id', jobIds);

        // Map application and saved status to jobs
        data.forEach(job => {
          const application = applications?.find(app => app.job_id === job.id);
          const saved = savedJobs?.find(saved => saved.job_id === job.id);
          
          job.application = application || null;
          job.isApplied = !!application;
          job.isSaved = !!saved;
        });
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching active jobs:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Fetch recommended jobs with caching and candidate preferences
   * @param {string} candidateId - Candidate ID
   * @param {Object} filters - Job filters
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Recommended jobs
   */
  async fetchRecommendedJobs(candidateId, filters = {}, forceRefresh = false) {
    if (!candidateId) return [];
    
    // Create a unique cache key based on filters
    const filterKey = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}:${value}`)
      .join('_');
    
    const cacheKey = cacheService.getCacheKey(CACHE_KEYS.JOBS, candidateId, `recommended_${filterKey}`);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedJobs = cacheService.get(cacheKey);
      if (cachedJobs) return cachedJobs;
    }
    
    try {
      // Get candidate profile for better recommendations
      const { data: candidateProfile } = await supabase
        .from('candidate_profiles')
        .select('skills, preferred_locations, expected_ctc, years_experience')
        .eq('id', candidateId)
        .single();

      // Start building the query
      let query = supabase
        .from('jobs')
        .select(`
          id,
          title,
          location,
          experience_level,
          salary_range,
          required_skills,
          created_at,
          companies:company_id (
            id,
            company_name,
            company_logo_url
          )
        `)
        .eq('status', 'active');
      
      // Apply candidate preferences if available
      if (candidateProfile) {
        // Match experience level
        if (candidateProfile.years_experience) {
          const expLevel = candidateProfile.years_experience;
          if (expLevel <= 2) {
            query = query.in('experience_level', ['Entry Level', 'Junior']);
          } else if (expLevel <= 5) {
            query = query.in('experience_level', ['Mid Level', 'Senior']);
          } else {
            query = query.in('experience_level', ['Senior', 'Lead', 'Manager']);
          }
        }

        // Match preferred locations
        if (candidateProfile.preferred_locations && candidateProfile.preferred_locations.length > 0) {
          const locationConditions = candidateProfile.preferred_locations.map(loc => 
            `location.ilike.%${loc}%`
          ).join(',');
          query = query.or(locationConditions);
        }
      }

      // Apply additional filters
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }
      
      if (filters.experienceLevel) {
        query = query.eq('experience_level', filters.experienceLevel);
      }
      
      if (filters.skills && filters.skills.length > 0) {
        const skillsCondition = filters.skills.map(skill => 
          `required_skills::jsonb @> '["${skill}"]'::jsonb`
        ).join(' OR ');
        
        query = query.or(skillsCondition);
      }
      
      // Execute the query
      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(20);
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.JOBS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching recommended jobs:', error);
      return [];
    }
  },

  /**
   * Apply to a job
   * @param {string} candidateId - Candidate ID
   * @param {string} jobId - Job ID
   * @param {Object} applicationData - Additional application data
   * @returns {Promise<Object>} - Application result
   */
  async applyToJob(candidateId, jobId, applicationData = {}) {
    try {
      // Check if already applied
      const { data: existingApplication } = await supabase
        .from('applications')
        .select('id')
        .eq('candidate_id', candidateId)
        .eq('job_id', jobId)
        .single();

      if (existingApplication) {
        return { success: false, error: 'You have already applied to this job' };
      }

      // Create application
      const { data, error } = await supabase
        .from('applications')
        .insert({
          candidate_id: candidateId,
          job_id: jobId,
          status: 'applied',
          application_date: new Date().toISOString(),
          cover_letter: applicationData.coverLetter || null,
          ...applicationData
        })
        .select()
        .single();

      if (error) throw error;

      // Clear relevant caches
      this.clearJobCaches(candidateId);

      return { success: true, data };
    } catch (error) {
      console.error('Error applying to job:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Save or unsave a job
   * @param {string} candidateId - Candidate ID
   * @param {string} jobId - Job ID
   * @param {boolean} save - Whether to save or unsave
   * @returns {Promise<Object>} - Result
   */
  async toggleSaveJob(candidateId, jobId, save = true) {
    try {
      if (save) {
        // Check if already saved
        const { data: existingSave } = await supabase
          .from('saved_jobs')
          .select('id')
          .eq('candidate_id', candidateId)
          .eq('job_id', jobId)
          .single();

        if (existingSave) {
          return { success: false, error: 'Job is already saved' };
        }

        // Save job
        const { data, error } = await supabase
          .from('saved_jobs')
          .insert({
            candidate_id: candidateId,
            job_id: jobId,
          })
          .select()
          .single();

        if (error) throw error;
        return { success: true, data, action: 'saved' };
      } else {
        // Unsave job
        const { data, error } = await supabase
          .from('saved_jobs')
          .delete()
          .match({ candidate_id: candidateId, job_id: jobId })
          .select()
          .single();

        if (error) throw error;
        return { success: true, data, action: 'unsaved' };
      }
    } catch (error) {
      console.error(`Error ${save ? 'saving' : 'unsaving'} job:`, error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Clear job-related caches for a candidate
   * @param {string} candidateId - Candidate ID
   */
  clearJobCaches(candidateId) {
    if (!candidateId) return;
    
    const cacheKeys = [CACHE_KEYS.JOBS, CACHE_KEYS.APPLICATIONS, CACHE_KEYS.SAVED_JOBS];
    cacheKeys.forEach(prefix => {
      const cacheKey = cacheService.getCacheKey(prefix, candidateId);
      cacheService.del(cacheKey);
    });
  }
};

export default candidateJobService;
