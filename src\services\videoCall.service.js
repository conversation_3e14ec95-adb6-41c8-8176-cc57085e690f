/**
 * Video Call Service
 * 
 * Handles video calling functionality using WebRTC
 * Supports peer-to-peer video calls for interviews
 */

import { supabase } from '@/utils/supabaseClient';

class VideoCallService {
  constructor() {
    this.localStream = null;
    this.remoteStream = null;
    this.peerConnection = null;
    this.isCallActive = false;
    this.isMuted = false;
    this.isVideoOff = false;
    this.isScreenSharing = false;
    this.dataChannel = null;
    
    // WebRTC configuration
    this.configuration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    };

    // Event callbacks
    this.onRemoteStream = null;
    this.onConnectionStateChange = null;
    this.onDataChannelMessage = null;
  }

  /**
   * Initialize local media stream
   */
  async initializeMedia(constraints = { video: true, audio: true }) {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      return this.localStream;
    } catch (error) {
      console.error('Error accessing media devices:', error);
      throw new Error('Failed to access camera/microphone');
    }
  }

  /**
   * Create peer connection
   */
  createPeerConnection() {
    this.peerConnection = new RTCPeerConnection(this.configuration);

    // Add local stream tracks
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        this.peerConnection.addTrack(track, this.localStream);
      });
    }

    // Handle remote stream
    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      if (this.onRemoteStream) {
        this.onRemoteStream(this.remoteStream);
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (this.onConnectionStateChange) {
        this.onConnectionStateChange(this.peerConnection.connectionState);
      }
    };

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignalingMessage({
          type: 'ice-candidate',
          candidate: event.candidate,
        });
      }
    };

    // Create data channel for chat
    this.dataChannel = this.peerConnection.createDataChannel('chat');
    this.dataChannel.onmessage = (event) => {
      if (this.onDataChannelMessage) {
        this.onDataChannelMessage(JSON.parse(event.data));
      }
    };

    return this.peerConnection;
  }

  /**
   * Create offer (caller)
   */
  async createOffer() {
    if (!this.peerConnection) {
      this.createPeerConnection();
    }

    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);

    return offer;
  }

  /**
   * Create answer (callee)
   */
  async createAnswer(offer) {
    if (!this.peerConnection) {
      this.createPeerConnection();
    }

    await this.peerConnection.setRemoteDescription(offer);
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);

    return answer;
  }

  /**
   * Handle received answer
   */
  async handleAnswer(answer) {
    await this.peerConnection.setRemoteDescription(answer);
  }

  /**
   * Handle ICE candidate
   */
  async handleIceCandidate(candidate) {
    await this.peerConnection.addIceCandidate(candidate);
  }

  /**
   * Toggle audio mute
   */
  toggleAudio() {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.isMuted = !audioTrack.enabled;
      }
    }
    return this.isMuted;
  }

  /**
   * Toggle video
   */
  toggleVideo() {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        this.isVideoOff = !videoTrack.enabled;
      }
    }
    return this.isVideoOff;
  }

  /**
   * Start screen sharing
   */
  async startScreenShare() {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true,
      });

      // Replace video track
      const videoTrack = screenStream.getVideoTracks()[0];
      const sender = this.peerConnection.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      );

      if (sender) {
        await sender.replaceTrack(videoTrack);
      }

      this.isScreenSharing = true;

      // Handle screen share end
      videoTrack.onended = () => {
        this.stopScreenShare();
      };

      return screenStream;
    } catch (error) {
      console.error('Error starting screen share:', error);
      throw error;
    }
  }

  /**
   * Stop screen sharing
   */
  async stopScreenShare() {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      const sender = this.peerConnection.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      );

      if (sender && videoTrack) {
        await sender.replaceTrack(videoTrack);
      }
    }
    this.isScreenSharing = false;
  }

  /**
   * Send chat message through data channel
   */
  sendChatMessage(message) {
    if (this.dataChannel && this.dataChannel.readyState === 'open') {
      this.dataChannel.send(JSON.stringify({
        type: 'chat',
        message,
        timestamp: new Date().toISOString(),
      }));
    }
  }

  /**
   * Send signaling message (to be implemented with your signaling server)
   */
  async sendSignalingMessage(message) {
    // This should be implemented with your signaling mechanism
    // For now, we'll use Supabase realtime as signaling
    console.log('Signaling message:', message);
  }

  /**
   * End call and cleanup
   */
  endCall() {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }

    this.isCallActive = false;
    this.isMuted = false;
    this.isVideoOff = false;
    this.isScreenSharing = false;
  }

  /**
   * Get call statistics
   */
  async getCallStats() {
    if (this.peerConnection) {
      const stats = await this.peerConnection.getStats();
      return stats;
    }
    return null;
  }
}

export default VideoCallService;
