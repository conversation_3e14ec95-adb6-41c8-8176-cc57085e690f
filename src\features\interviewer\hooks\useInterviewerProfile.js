/**
 * Interviewer Profile Hook
 *
 * Provides a unified interface for managing interviewer profile data and schedule
 * Uses optimized caching for better performance
 */
import { useState, useCallback } from 'react';
import { message } from 'antd';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useInterviewerStore from '../store/interviewer.store';
import { interviewerDataService } from '../services';

const useInterviewerProfile = () => {
  const { user, profile, updateProfile, setError } = useAuth();
  const { uploadProfilePhoto } = useInterviewerStore();
  const [loading, setLoading] = useState(false);

  /**
   * Fetch the complete interviewer profile with all related data
   * Uses optimized caching for better performance
   * @param {boolean} forceRefresh - Whether to force a refresh from the database
   */
  const fetchInterviewerProfile = useCallback(
    async (forceRefresh = false) => {
      if (!user?.id) return null;

      try {
        setLoading(true);

        // Use data service with caching
        const data = await dataFetchService.fetchInterviewerProfile(user.id, forceRefresh);

        // Update the profile in auth store
        if (data) {
          updateProfile(data);
        }

        return data;
      } catch (error) {
        console.error('Error fetching interviewer profile:', error);
        setError(error.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [user, setError, updateProfile]
  );

  /**
   * Update interviewer profile data
   * Invalidates cache after update
   * @param {Object} profileData - Profile data to update
   */
  const updateInterviewerProfile = useCallback(
    async (profileData) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      setLoading(true);

      try {
        // Update the profile in the database
        const { data, error } = await supabase
          .from('profiles')
          .update({
            ...profileData,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id)
          .select()
          .single();

        if (error) throw error;

        // Update the profile in auth store
        updateProfile(data);

        // Invalidate the profile cache
        dataFetchService.clearCache(user.id, 'PROFILE', 'interviewer');

        return data;
      } catch (error) {
        console.error('Error updating interviewer profile:', error);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [user, updateProfile]
  );

  /**
   * Fetch interviewer's scheduled interviews
   * Uses optimized caching for better performance
   * @param {boolean} forceRefresh - Whether to force a refresh from the database
   */
  const fetchInterviewSchedule = useCallback(
    async (forceRefresh = false) => {
      if (!user?.id) return [];

      try {
        setLoading(true);

        // Use data service with caching
        const data = await dataFetchService.fetchInterviewerSchedule(user.id, forceRefresh);

        return data;
      } catch (error) {
        console.error('Error fetching interview schedule:', error);
        setError(error.message);
        return [];
      } finally {
        setLoading(false);
      }
    },
    [user, setError]
  );

  /**
   * Update interviewer availability
   * Invalidates cache after update
   * @param {Object} availability - Availability data to update
   */
  const updateAvailability = useCallback(
    async (availability) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      setLoading(true);

      try {
        // Update the interviewer profile with new availability
        const { data, error } = await supabase
          .from('interviewer_profiles')
          .update({
            availability_calendar: availability,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id)
          .select()
          .single();

        if (error) throw error;

        // Update the profile in auth store
        updateProfile({
          ...profile,
          availability_calendar: availability,
        });

        // Invalidate the profile cache
        dataFetchService.clearCache(user.id, 'PROFILE', 'interviewer');

        message.success('Availability updated successfully');
        return data;
      } catch (error) {
        console.error('Error updating availability:', error);
        message.error('Failed to update availability');
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [user, profile, updateProfile]
  );

  /**
   * Upload profile photo
   * Invalidates cache after upload
   * @param {File} file - Photo file to upload
   */
  const handlePhotoUpload = useCallback(
    async (file) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      setLoading(true);

      try {
        const result = await uploadProfilePhoto(user.id, file);

        if (result?.url) {
          // Update the profile with the new photo URL
          await updateInterviewerProfile({
            profile_photo_url: result.url,
          });

          // Cache is invalidated in updateInterviewerProfile
          message.success('Profile photo updated successfully');
        }

        return result;
      } catch (error) {
        message.error('Failed to upload profile photo');
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [user, uploadProfilePhoto, updateInterviewerProfile]
  );

  return {
    loading,
    fetchInterviewerProfile,
    updateInterviewerProfile,
    fetchInterviewSchedule,
    updateAvailability,
    handlePhotoUpload,
  };
};

export default useInterviewerProfile;
