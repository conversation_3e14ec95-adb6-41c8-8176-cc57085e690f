import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCompanyStore from '../store/company.store';

/**
 * Optimized useInterviews Hook for Companies
 *
 * Leverages standardized store patterns:
 * - Uses store cache for interviews
 * - Utilizes standardized loading states
 * - Supports force refresh capabilities
 * - Integrates with store helper methods
 */
const useInterviews = () => {
  // Local state for categorized interviews (dashboard-specific)
  const [upcoming, setUpcoming] = useState([]);
  const [past, setPast] = useState([]);
  const [requests, setRequests] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);

  const { user, profile } = useAuth();

  // Use store state and methods
  const {
    // Data from store
    interviews,

    // Loading states from store
    loading: storeLoading,
    interviewsLoading,

    // Error states from store
    error: storeError,
    interviewsError,

    // Store methods
    fetchCompanyInterviews,
  } = useCompanyStore();

  // Combined loading and error states
  const loading = storeLoading || interviewsLoading || localLoading;
  const error = storeError || interviewsError;

  // Categorize interviews from store data
  const categorizeInterviews = useCallback((interviewsData) => {
    if (!interviewsData || !Array.isArray(interviewsData)) {
      setRequests([]);
      setUpcoming([]);
      setPast([]);
      return;
    }

    const requestsData = interviewsData.filter((interview) => interview.status === 'requested');
    const upcomingData = interviewsData.filter(
      (interview) => interview.status === 'scheduled' || interview.status === 'confirmed'
    );
    const pastData = interviewsData.filter(
      (interview) => interview.status === 'completed' || interview.status === 'cancelled'
    );

    setRequests(requestsData);
    setUpcoming(upcomingData);
    setPast(pastData);
  }, []);

  // Effect to categorize interviews when store data changes
  useEffect(() => {
    categorizeInterviews(interviews);
  }, [interviews, categorizeInterviews]);

  // Optimized fetch function using store methods
  const fetchAllInterviews = useCallback(
    async (forceRefresh = false) => {
      if (!user || !profile) return;

      try {
        // Use store method with cache management
        const data = await fetchCompanyInterviews(profile.id, forceRefresh);

        // Categorize the fetched data
        categorizeInterviews(data);

        return {
          upcoming,
          past,
          requests,
        };
      } catch (err) {
        console.error('Error fetching company interviews:', err);
        return { upcoming: [], past: [], requests: [] };
      }
    },
    [user, profile, fetchCompanyInterviews, categorizeInterviews, upcoming, past, requests]
  );

  // Effect to fetch data when user/profile changes
  useEffect(() => {
    if (user && profile) {
      fetchAllInterviews();
    }
  }, [user, profile, fetchAllInterviews]);

  return {
    // Categorized interview data
    upcoming,
    past,
    requests,

    // All interviews from store
    allInterviews: interviews || [],

    // Combined UI state
    loading,
    error,

    // Actions
    refetch: fetchAllInterviews,

    // Force refresh capabilities
    forceRefresh: () => fetchAllInterviews(true),
    refreshInterviews: () => fetchCompanyInterviews(profile?.id, true),
  };
};

export default useInterviews;
