/**
 * Interviewer Data Service
 * 
 * Handles interviewer-specific data operations and API calls
 * Extracted from shared dataFetchService for better organization
 */

import { supabase } from '@/utils/supabaseClient';
import cacheService, { CACHE_TTL } from '@/utils/cacheService';

// Interviewer-specific cache keys
const INTERVIEWER_CACHE_KEYS = {
  PROFILE: 'interviewer_profile',
  INTERVIEWS: 'interviewer_interviews',
  REQUESTS: 'interviewer_requests',
  SCHEDULE: 'interviewer_schedule',
  STATS: 'interviewer_stats',
};

/**
 * Interviewer Data Service for optimized data fetching and caching
 */
const interviewerDataService = {
  /**
   * Fetch interviewer profile with caching
   * @param {string} interviewerId - Interviewer ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Interviewer profile
   */
  async fetchInterviewerProfile(interviewerId, forceRefresh = false) {
    if (!interviewerId) return null;
    
    const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS.PROFILE, interviewerId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedProfile = cacheService.get(cacheKey);
      if (cachedProfile) return cachedProfile;
    }
    
    try {
      // Fetch the complete interviewer profile view
      const { data, error } = await supabase
        .from('interviewer_profiles_complete')
        .select('*')
        .eq('id', interviewerId)
        .single();
      
      if (error) throw error;
      
      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.PROFILE);
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching interviewer profile:', error);
      throw error;
    }
  },
  
  /**
   * Fetch interviewer's scheduled interviews with caching
   * @param {string} interviewerId - Interviewer ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Scheduled interviews
   */
  async fetchInterviewerSchedule(interviewerId, forceRefresh = false) {
    if (!interviewerId) return [];
    
    const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS.INTERVIEWS, interviewerId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedInterviews = cacheService.get(cacheKey);
      if (cachedInterviews) return cachedInterviews;
    }
    
    try {
      // Get current date in ISO format
      const now = new Date().toISOString();
      
      // Fetch upcoming interviews
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          id,
          interview_date,
          duration_minutes,
          status,
          feedback,
          score,
          candidate:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (resume_url, years_experience, current_job_title, profile_photo_url)
          ),
          job:job_id (
            id,
            title,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('interviewer_id', interviewerId)
        .gte('interview_date', now)
        .order('interview_date', { ascending: true });
      
      if (error) throw error;
      
      // Cache the result with shorter TTL for time-sensitive data
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }
      
      return data || [];
    } catch (error) {
      console.error('Error fetching interviewer schedule:', error);
      return [];
    }
  },

  /**
   * Fetch interview requests available to interviewer
   * @param {string} interviewerId - Interviewer ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Available interview requests
   */
  async fetchInterviewRequests(interviewerId, forceRefresh = false) {
    if (!interviewerId) return [];
    
    const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS.REQUESTS, interviewerId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedRequests = cacheService.get(cacheKey);
      if (cachedRequests) return cachedRequests;
    }
    
    try {
      // Fetch pending interview requests
      const { data, error } = await supabase
        .from('interviews')
        .select(`
          *,
          candidate_profiles:candidate_id (
            id,
            full_name,
            profile_photo_url,
            current_job_title,
            years_experience,
            skills
          ),
          jobs:job_id (
            id,
            title,
            experience_level,
            required_skills
          ),
          company_profiles:company_id (
            company_name,
            company_logo_url
          )
        `)
        .eq('status', 'requested')
        .is('interviewer_id', null)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching interview requests:', error);
      return [];
    }
  },

  /**
   * Fetch interviewer's interview history
   * @param {string} interviewerId - Interviewer ID
   * @param {Object} filters - Optional filters
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Array>} - Interview history
   */
  async fetchInterviewHistory(interviewerId, filters = {}, forceRefresh = false) {
    if (!interviewerId) return [];
    
    // Create cache key with filters
    const filterKey = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}:${value}`)
      .join('_');
    
    const cacheKey = cacheService.getCacheKey(
      INTERVIEWER_CACHE_KEYS.INTERVIEWS, 
      interviewerId, 
      `history_${filterKey || 'all'}`
    );
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedHistory = cacheService.get(cacheKey);
      if (cachedHistory) return cachedHistory;
    }
    
    try {
      let query = supabase
        .from('interviews')
        .select(`
          id,
          interview_date,
          duration_minutes,
          status,
          feedback,
          score,
          candidate:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (current_job_title, profile_photo_url)
          ),
          job:job_id (
            id,
            title,
            companies:company_id (
              id,
              company_name,
              company_logo_url
            )
          )
        `)
        .eq('interviewer_id', interviewerId)
        .in('status', ['completed', 'cancelled']);

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.dateFrom) {
        query = query.gte('interview_date', filters.dateFrom);
      }

      if (filters.dateTo) {
        query = query.lte('interview_date', filters.dateTo);
      }

      const { data, error } = await query
        .order('interview_date', { ascending: false })
        .limit(filters.limit || 50);

      if (error) throw error;

      // Cache the result
      if (data) {
        cacheService.set(cacheKey, data, CACHE_TTL.INTERVIEWS);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching interview history:', error);
      return [];
    }
  },

  /**
   * Fetch interviewer statistics
   * @param {string} interviewerId - Interviewer ID
   * @param {boolean} forceRefresh - Force refresh from database
   * @returns {Promise<Object>} - Interviewer statistics
   */
  async fetchInterviewerStats(interviewerId, forceRefresh = false) {
    if (!interviewerId) return null;
    
    const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS.STATS, interviewerId);
    
    // Return from cache if available and not forcing refresh
    if (!forceRefresh) {
      const cachedStats = cacheService.get(cacheKey);
      if (cachedStats) return cachedStats;
    }
    
    try {
      const { data, error } = await supabase
        .from('interviews')
        .select('status, score, interview_date')
        .eq('interviewer_id', interviewerId);

      if (error) throw error;

      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

      const stats = {
        total: data.length,
        completed: data.filter(i => i.status === 'completed').length,
        scheduled: data.filter(i => i.status === 'scheduled').length,
        cancelled: data.filter(i => i.status === 'cancelled').length,
        thisMonth: data.filter(i => 
          new Date(i.interview_date) >= thisMonth && i.status === 'completed'
        ).length,
        lastMonth: data.filter(i => 
          new Date(i.interview_date) >= lastMonth && 
          new Date(i.interview_date) < thisMonth && 
          i.status === 'completed'
        ).length,
        averageScore: 0,
      };

      // Calculate average score for completed interviews
      const completedWithScores = data.filter(i => i.status === 'completed' && i.score);
      if (completedWithScores.length > 0) {
        const totalScore = completedWithScores.reduce((sum, i) => sum + i.score, 0);
        stats.averageScore = Math.round((totalScore / completedWithScores.length) * 10) / 10;
      }

      // Cache the result
      cacheService.set(cacheKey, stats, CACHE_TTL.STATS);

      return stats;
    } catch (error) {
      console.error('Error fetching interviewer stats:', error);
      return null;
    }
  },

  /**
   * Clear cache for a specific interviewer
   * @param {string} interviewerId - Interviewer ID
   * @param {string} cacheType - Type of cache to clear (or all if not specified)
   */
  clearCache(interviewerId, cacheType = null) {
    if (!interviewerId) return;
    
    if (cacheType) {
      // Clear specific cache type
      const cacheKey = cacheService.getCacheKey(INTERVIEWER_CACHE_KEYS[cacheType], interviewerId);
      cacheService.del(cacheKey);
    } else {
      // Clear all cache for this interviewer
      Object.values(INTERVIEWER_CACHE_KEYS).forEach(prefix => {
        const cacheKey = cacheService.getCacheKey(prefix, interviewerId);
        cacheService.del(cacheKey);
      });
    }
  }
};

export default interviewerDataService;
