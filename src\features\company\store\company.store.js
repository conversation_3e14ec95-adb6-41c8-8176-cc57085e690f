/**
 * Company Store - STANDARDIZED VERSION
 *
 * Responsibilities:
 * - Job postings management
 * - Application reviews and processing
 * - Interview coordination
 * - Candidate shortlisting
 * - Company-specific workflows
 * - Hiring analytics
 *
 * Follows standardized store patterns:
 * - Consistent naming conventions
 * - Standardized loading/error states
 * - Unified cache management
 * - Common action patterns
 *
 * Does NOT handle:
 * - Authentication (handled by auth store)
 * - Profile data (handled by auth store)
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import {
  getCompanyJobs,
  getCompanyApplications,
  getCompanyInterviews,
  getShortlistedCandidates,
} from '../services/company.service';
import { createJob, updateJob, deleteJob } from '@/services/job.service';

// Initial state following standardized pattern
const initialCompanyState = {
  // === CORE DATA STATE ===
  // Jobs state
  jobs: [],
  selectedJob: null,

  // Applications state
  applications: [],

  // Interviews state
  interviews: [],

  // Shortlisted candidates state
  shortlistedCandidates: [],

  // === STANDARDIZED UI STATE ===
  // Main loading/error states
  loading: false,
  error: null,

  // Specific loading states for complex operations
  jobsLoading: false,
  applicationsLoading: false,
  interviewsLoading: false,
  shortlistedLoading: false,

  // Specific error states for better error handling
  jobsError: null,
  applicationsError: null,
  interviewsError: null,
  shortlistedError: null,

  // === STANDARDIZED CACHE MANAGEMENT ===
  _cache: {
    lastJobsFetch: null,
    lastApplicationsFetch: null,
    lastInterviewsFetch: null,
    lastShortlistedFetch: null,
    jobsExpiry: 5 * 60 * 1000, // 5 minutes
    applicationsExpiry: 2 * 60 * 1000, // 2 minutes (frequent updates)
    interviewsExpiry: 3 * 60 * 1000, // 3 minutes
    shortlistedExpiry: 10 * 60 * 1000, // 10 minutes
    jobsById: new Map(),
  },
};

const useCompanyStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...initialCompanyState,

        // === STANDARDIZED UI STATE HELPERS ===
        setLoading: (loading) => set({ loading }, false, 'company:setLoading'),
        setError: (error) => set({ error }, false, 'company:setError'),
        clearError: () => set({ error: null }, false, 'company:clearError'),

        setJobsLoading: (loading) => set({ jobsLoading: loading }, false, 'company:setJobsLoading'),
        setJobsError: (error) => set({ jobsError: error }, false, 'company:setJobsError'),
        clearJobsError: () => set({ jobsError: null }, false, 'company:clearJobsError'),

        setApplicationsLoading: (loading) =>
          set({ applicationsLoading: loading }, false, 'company:setApplicationsLoading'),
        setApplicationsError: (error) =>
          set({ applicationsError: error }, false, 'company:setApplicationsError'),
        clearApplicationsError: () =>
          set({ applicationsError: null }, false, 'company:clearApplicationsError'),

        // === STANDARDIZED CACHE HELPERS ===
        updateJobsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastJobsFetch: Date.now() },
            },
            false,
            'company:updateJobsCache'
          );
        },

        isJobsCacheValid: () => {
          const { _cache } = get();
          return _cache.lastJobsFetch && Date.now() - _cache.lastJobsFetch < _cache.jobsExpiry;
        },

        updateApplicationsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastApplicationsFetch: Date.now() },
            },
            false,
            'company:updateApplicationsCache'
          );
        },

        isApplicationsCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastApplicationsFetch &&
            Date.now() - _cache.lastApplicationsFetch < _cache.applicationsExpiry
          );
        },

        // === JOB MANAGEMENT ACTIONS ===

        fetchCompanyJobs: async (id, forceRefresh = false) => {
          // Check cache validity unless force refresh
          if (!forceRefresh && get().isJobsCacheValid()) {
            return get().jobs;
          }

          get().setJobsLoading(true);
          get().clearJobsError();

          try {
            const { success, data, error } = await getCompanyJobs(id);
            if (success) {
              set({ jobs: data || [] }, false, 'company:fetchCompanyJobs:success');
              get().updateJobsCache();
            } else {
              get().setJobsError(error);
            }
            return data || [];
          } catch (error) {
            console.error('Error fetching company jobs:', error);
            get().setJobsError(error.message);
            return [];
          } finally {
            get().setJobsLoading(false);
          }
        },

        fetchCompanyApplications: async (id) => {
          set({ isApplicationsLoading: true, applicationsError: null });
          try {
            const { success, data, error } = await getCompanyApplications(id);
            if (success) {
              set({ applications: data });
            } else {
              set({ applicationsError: error });
            }
          } catch (error) {
            set({ applicationsError: error.message });
          } finally {
            set({ isApplicationsLoading: false });
          }
        },

        fetchCompanyInterviews: async (id) => {
          set({ isInterviewsLoading: true, interviewsError: null });
          try {
            const { success, data, error } = await getCompanyInterviews(id);
            if (success) {
              set({ interviews: data });
            } else {
              set({ interviewsError: error });
            }
          } catch (error) {
            set({ interviewsError: error.message });
          } finally {
            set({ isInterviewsLoading: false });
          }
        },

        fetchShortlistedCandidates: async (id) => {
          set({ isShortlistedLoading: true, shortlistedError: null });
          try {
            const { success, data, error } = await getShortlistedCandidates(id);
            if (success) {
              set({ shortlistedCandidates: data });
            } else {
              set({ shortlistedError: error });
            }
          } catch (error) {
            set({ shortlistedError: error.message });
          } finally {
            set({ isShortlistedLoading: false });
          }
        },

        createNewJob: async (jobData) => {
          set({ isJobsLoading: true, jobsError: null });
          try {
            const { success, data, error } = await createJob(jobData);
            if (success) {
              set({ jobs: [...get().jobs, data] });
              return { success: true, data };
            } else {
              set({ jobsError: error });
              return { success: false, error };
            }
          } catch (error) {
            set({ jobsError: error.message });
            return { success: false, error: error.message };
          } finally {
            set({ isJobsLoading: false });
          }
        },

        updateExistingJob: async (id, updates) => {
          set({ isJobsLoading: true, jobsError: null });
          try {
            const { success, data, error } = await updateJob(id, updates);
            if (success) {
              set({
                jobs: get().jobs.map((job) => (job.id === id ? data : job)),
              });
              return { success: true, data };
            } else {
              set({ jobsError: error });
              return { success: false, error };
            }
          } catch (error) {
            set({ jobsError: error.message });
            return { success: false, error: error.message };
          } finally {
            set({ isJobsLoading: false });
          }
        },

        deleteExistingJob: async (id) => {
          set({ isJobsLoading: true, jobsError: null });
          try {
            const { success, error } = await deleteJob(id);
            if (success) {
              set({
                jobs: get().jobs.filter((job) => job.id !== id),
              });
              return { success: true };
            } else {
              set({ jobsError: error });
              return { success: false, error };
            }
          } catch (error) {
            set({ jobsError: error.message });
            return { success: false, error: error.message };
          } finally {
            set({ isJobsLoading: false });
          }
        },

        setSelectedJob: (job) => set({ selectedJob: job }),

        // === STANDARDIZED RESET METHOD ===
        resetStore: () => {
          set(initialCompanyState, false, 'company:resetStore');
        },
      }),
      {
        name: 'company-storage',
        partialize: (state) => ({
          jobs: state.jobs,
          applications: state.applications,
          interviews: state.interviews,
          shortlistedCandidates: state.shortlistedCandidates,
          selectedJob: state.selectedJob,
          _cache: state._cache,
        }),
      }
    ),
    { name: 'company-store' }
  )
);

export default useCompanyStore;
