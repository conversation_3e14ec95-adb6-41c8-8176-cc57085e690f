/**
 * Interview Service
 *
 * This service handles all API calls related to interviews.
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Get an interview by ID
 * @param {string} id - Interview ID
 * @returns {Promise<Object>} - Interview data
 */
export const getInterview = async (id) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          phone_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url
        ),
        interviewers:interviewer_id (
          id,
          full_name,
          current_designation,
          current_company
        ),
        jobs:job_id (
          id,
          title,
          company_id
        ),
        companies:jobs.company_id (
          id,
          company_name,
          company_logo_url
        )
      `
      )
      .eq('id', id)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a new interview request
 * @param {Object} interviewData - Interview data
 * @returns {Promise<Object>} - Result of the operation
 */
export const createInterviewRequest = async (interviewData) => {
  try {
    // Set the status to 'requested'
    const data = {
      ...interviewData,
      status: 'requested',
    };

    const { data: result, error } = await supabase.from('interviews').insert(data).select();

    if (error) throw error;
    return { success: true, data: result[0] };
  } catch (error) {
    console.error('Error creating interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update an interview
 * @param {string} id - Interview ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object>} - Result of the operation
 */
export const updateInterview = async (id, updates) => {
  try {
    const { data, error } = await supabase.from('interviews').update(updates).eq('id', id).select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Accept an interview request
 * @param {string} id - Interview ID
 * @returns {Promise<Object>} - Result of the operation
 */
export const acceptInterviewRequest = async (id) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({ status: 'accepted' })
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error accepting interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Schedule an interview
 * @param {string} id - Interview ID
 * @param {Object} scheduleData - Schedule data (date, time, duration)
 * @returns {Promise<Object>} - Result of the operation
 */
export const scheduleInterview = async (id, scheduleData) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'scheduled',
        interview_date: scheduleData.date,
        duration_minutes: scheduleData.duration,
        meeting_link: scheduleData.meetingLink,
      })
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error scheduling interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Complete an interview and submit feedback
 * @param {string} id - Interview ID
 * @param {Object} feedbackData - Feedback data (score, feedback)
 * @returns {Promise<Object>} - Result of the operation
 */
export const completeInterview = async (id, feedbackData) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'completed',
        score: feedbackData.score,
        feedback: feedbackData.feedback,
      })
      .eq('id', id)
      .select();

    if (error) throw error;

    // If there's a job application associated with this interview,
    // update the application status and score
    if (data[0].job_id) {
      const { data: applicationData, error: applicationError } = await supabase
        .from('applications')
        .select('*')
        .eq('candidate_id', data[0].candidate_id)
        .eq('job_id', data[0].job_id)
        .single();

      if (!applicationError && applicationData) {
        await supabase
          .from('applications')
          .update({
            status: 'interviewed',
            interview_score: feedbackData.score,
          })
          .eq('id', applicationData.id);
      }
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error completing interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Cancel an interview
 * @param {string} id - Interview ID
 * @param {string} reason - Reason for cancellation
 * @returns {Promise<Object>} - Result of the operation
 */
export const cancelInterview = async (id, reason) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'cancelled',
        cancellation_reason: reason,
      })
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error cancelling interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Match a candidate with an interviewer
 * @param {string} candidateId - Candidate ID
 * @param {string} role - Role applied for
 * @returns {Promise<Object>} - Matched interviewer
 */
export const matchCandidateWithInterviewer = async (candidateId, role) => {
  try {
    // Find interviewers who can interview for this role
    const { data, error } = await supabase
      .from('interviewer_profiles')
      .select('*')
      .contains('preferred_interview_roles', [{ role }]);

    if (error) throw error;

    if (data.length === 0) {
      return { success: false, error: 'No matching interviewers found' };
    }

    // For now, just return the first matching interviewer
    // In a real system, you'd implement a more sophisticated matching algorithm
    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error matching candidate with interviewer:', error);
    return { success: false, error: error.message };
  }
};
