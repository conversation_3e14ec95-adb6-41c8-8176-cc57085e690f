/**
 * Interviewer Store - STANDARDIZED VERSION
 *
 * Responsibilities:
 * - Interview request management
 * - Interview scheduling and coordination
 * - Interview completion and feedback
 * - Earnings tracking and analytics
 * - Interviewer-specific workflows
 * - Performance metrics
 *
 * Follows standardized store patterns:
 * - Consistent naming conventions
 * - Standardized loading/error states
 * - Unified cache management
 * - Common action patterns
 *
 * Does NOT handle:
 * - Authentication (handled by auth store)
 * - Profile data (handled by auth store)
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import {
  getInterviewRequests,
  getScheduledInterviews,
  getCompletedInterviews,
  getInterviewerEarnings,
} from '../services/interviewer.service';
import {
  acceptInterviewRequest,
  scheduleInterview,
  completeInterview,
  cancelInterview,
} from '@/services/interview.service';

// Initial state following standardized pattern
const initialInterviewerState = {
  // === CORE DATA STATE ===
  // Interview requests state
  interviewRequests: [],

  // Scheduled interviews state
  scheduledInterviews: [],

  // Completed interviews state
  completedInterviews: [],

  // Earnings state
  earnings: null,

  // Selected interview for detailed view
  selectedInterview: null,

  // === STANDARDIZED UI STATE ===
  // Main loading/error states
  loading: false,
  error: null,

  // Specific loading states for complex operations
  requestsLoading: false,
  scheduledLoading: false,
  completedLoading: false,
  earningsLoading: false,

  // Specific error states for better error handling
  requestsError: null,
  scheduledError: null,
  completedError: null,
  earningsError: null,

  // === STANDARDIZED CACHE MANAGEMENT ===
  _cache: {
    lastRequestsFetch: null,
    lastScheduledFetch: null,
    lastCompletedFetch: null,
    lastEarningsFetch: null,
    requestsExpiry: 2 * 60 * 1000, // 2 minutes (frequent updates)
    scheduledExpiry: 3 * 60 * 1000, // 3 minutes
    completedExpiry: 10 * 60 * 1000, // 10 minutes
    earningsExpiry: 15 * 60 * 1000, // 15 minutes
    interviewsById: new Map(),
  },
};

const useInterviewerStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...initialInterviewerState,

        // === STANDARDIZED UI STATE HELPERS ===
        setLoading: (loading) => set({ loading }, false, 'interviewer:setLoading'),
        setError: (error) => set({ error }, false, 'interviewer:setError'),
        clearError: () => set({ error: null }, false, 'interviewer:clearError'),

        setRequestsLoading: (loading) =>
          set({ requestsLoading: loading }, false, 'interviewer:setRequestsLoading'),
        setRequestsError: (error) =>
          set({ requestsError: error }, false, 'interviewer:setRequestsError'),
        clearRequestsError: () =>
          set({ requestsError: null }, false, 'interviewer:clearRequestsError'),

        setScheduledLoading: (loading) =>
          set({ scheduledLoading: loading }, false, 'interviewer:setScheduledLoading'),
        setScheduledError: (error) =>
          set({ scheduledError: error }, false, 'interviewer:setScheduledError'),
        clearScheduledError: () =>
          set({ scheduledError: null }, false, 'interviewer:clearScheduledError'),

        // === STANDARDIZED CACHE HELPERS ===
        updateRequestsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastRequestsFetch: Date.now() },
            },
            false,
            'interviewer:updateRequestsCache'
          );
        },

        isRequestsCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastRequestsFetch &&
            Date.now() - _cache.lastRequestsFetch < _cache.requestsExpiry
          );
        },

        updateScheduledCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastScheduledFetch: Date.now() },
            },
            false,
            'interviewer:updateScheduledCache'
          );
        },

        isScheduledCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastScheduledFetch &&
            Date.now() - _cache.lastScheduledFetch < _cache.scheduledExpiry
          );
        },

        updateCompletedCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastCompletedFetch: Date.now() },
            },
            false,
            'interviewer:updateCompletedCache'
          );
        },

        isCompletedCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastCompletedFetch &&
            Date.now() - _cache.lastCompletedFetch < _cache.completedExpiry
          );
        },

        // === INTERVIEW MANAGEMENT ACTIONS ===

        fetchInterviewRequests: async (id, forceRefresh = false) => {
          // Check cache validity unless force refresh
          if (!forceRefresh && get().isRequestsCacheValid()) {
            return get().interviewRequests;
          }

          get().setRequestsLoading(true);
          get().clearRequestsError();

          try {
            const { success, data, error } = await getInterviewRequests(id);
            if (success) {
              set(
                { interviewRequests: data || [] },
                false,
                'interviewer:fetchInterviewRequests:success'
              );
              get().updateRequestsCache();
            } else {
              get().setRequestsError(error);
            }
            return data || [];
          } catch (error) {
            console.error('Error fetching interview requests:', error);
            get().setRequestsError(error.message);
            return [];
          } finally {
            get().setRequestsLoading(false);
          }
        },

        fetchScheduledInterviews: async (id, forceRefresh = false) => {
          // Check cache validity unless force refresh
          if (!forceRefresh && get().isScheduledCacheValid()) {
            return get().scheduledInterviews;
          }

          get().setScheduledLoading(true);
          get().clearScheduledError();

          try {
            const { success, data, error } = await getScheduledInterviews(id);
            if (success) {
              set(
                { scheduledInterviews: data || [] },
                false,
                'interviewer:fetchScheduledInterviews:success'
              );
              get().updateScheduledCache();
            } else {
              get().setScheduledError(error);
            }
            return data || [];
          } catch (error) {
            console.error('Error fetching scheduled interviews:', error);
            get().setScheduledError(error.message);
            return [];
          } finally {
            get().setScheduledLoading(false);
          }
        },

        fetchCompletedInterviews: async (id, forceRefresh = false) => {
          // Check cache validity unless force refresh
          if (!forceRefresh && get().isCompletedCacheValid()) {
            return get().completedInterviews;
          }

          set({ completedLoading: true, completedError: null });

          try {
            const { success, data, error } = await getCompletedInterviews(id);
            if (success) {
              set(
                { completedInterviews: data || [] },
                false,
                'interviewer:fetchCompletedInterviews:success'
              );
              get().updateCompletedCache();
            } else {
              set({ completedError: error });
            }
            return data || [];
          } catch (error) {
            console.error('Error fetching completed interviews:', error);
            set({ completedError: error.message });
            return [];
          } finally {
            set({ completedLoading: false });
          }
        },

        fetchEarnings: async (id) => {
          set({ isEarningsLoading: true, earningsError: null });
          try {
            const { success, data, error } = await getInterviewerEarnings(id);
            if (success) {
              set({ earnings: data });
            } else {
              set({ earningsError: error });
            }
          } catch (error) {
            set({ earningsError: error.message });
          } finally {
            set({ isEarningsLoading: false });
          }
        },

        acceptRequest: async (interviewId) => {
          try {
            const { success, data, error } = await acceptInterviewRequest(interviewId);
            if (success) {
              // Remove from requests and add to scheduled
              set({
                interviewRequests: get().interviewRequests.filter(
                  (interview) => interview.id !== interviewId
                ),
                scheduledInterviews: [...get().scheduledInterviews, data],
              });
              return { success: true, data };
            } else {
              return { success: false, error };
            }
          } catch (error) {
            return { success: false, error: error.message };
          }
        },

        scheduleInterviewTime: async (interviewId, scheduleData) => {
          try {
            const { success, data, error } = await scheduleInterview(interviewId, scheduleData);
            if (success) {
              // Update in scheduled interviews
              set({
                scheduledInterviews: get().scheduledInterviews.map((interview) =>
                  interview.id === interviewId ? data : interview
                ),
              });
              return { success: true, data };
            } else {
              return { success: false, error };
            }
          } catch (error) {
            return { success: false, error: error.message };
          }
        },

        submitFeedback: async (interviewId, feedbackData) => {
          try {
            const { success, data, error } = await completeInterview(interviewId, feedbackData);
            if (success) {
              // Move from scheduled to completed
              set({
                scheduledInterviews: get().scheduledInterviews.filter(
                  (interview) => interview.id !== interviewId
                ),
                completedInterviews: [...get().completedInterviews, data],
              });
              return { success: true, data };
            } else {
              return { success: false, error };
            }
          } catch (error) {
            return { success: false, error: error.message };
          }
        },

        cancelScheduledInterview: async (interviewId, reason) => {
          try {
            const { success, data, error } = await cancelInterview(interviewId, reason);
            if (success) {
              // Remove from scheduled
              set({
                scheduledInterviews: get().scheduledInterviews.filter(
                  (interview) => interview.id !== interviewId
                ),
              });
              return { success: true, data };
            } else {
              return { success: false, error };
            }
          } catch (error) {
            return { success: false, error: error.message };
          }
        },

        setSelectedInterview: (interview) => set({ selectedInterview: interview }),

        // === STANDARDIZED RESET METHOD ===
        resetStore: () => {
          set(initialInterviewerState, false, 'interviewer:resetStore');
        },
      }),
      {
        name: 'interviewer-storage',
        partialize: (state) => ({
          interviewRequests: state.interviewRequests,
          scheduledInterviews: state.scheduledInterviews,
          completedInterviews: state.completedInterviews,
          earnings: state.earnings,
          selectedInterview: state.selectedInterview,
          _cache: state._cache,
        }),
      }
    ),
    { name: 'interviewer-store' }
  )
);

export default useInterviewerStore;
