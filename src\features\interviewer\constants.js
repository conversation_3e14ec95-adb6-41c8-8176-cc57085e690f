/**
 * Interviewer-specific constants
 */

export const INTERVIEWER_ROLES = [
  'Sales Manager',
  'Sales Executive',
  'Property Consultant',
  'Leasing Manager',
  'Property Manager',
  'Real Estate Broker',
  'Real Estate Agent',
  'Business Development Manager',
  'Marketing Manager',
  'CRM Manager',
  'Operations Manager',
  'Site Engineer',
  'Project Manager',
  'Facility Manager',
];

export const INTERVIEW_FORMATS = ['Video Call', 'Phone Call', 'In-Person'];

export const INTERVIEW_STATUS = {
  REQUESTED: 'requested',
  ACCEPTED: 'accepted',
  SCHEDULED: 'scheduled',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show',
};

export const FEEDBACK_CATEGORIES = [
  'Technical Knowledge',
  'Industry Experience',
  'Communication Skills',
  'Problem Solving',
  'Cultural Fit',
  'Leadership Potential',
  'Adaptability',
  'Client Handling',
  'Sales Acumen',
  'Negotiation Skills',
];

export const SCORE_RANGES = {
  EXCELLENT: { min: 80, max: 100, label: 'Excellent' },
  GOOD: { min: 70, max: 79, label: 'Good' },
  AVERAGE: { min: 60, max: 69, label: 'Average' },
  BELOW_AVERAGE: { min: 50, max: 59, label: 'Below Average' },
  POOR: { min: 0, max: 49, label: 'Poor' },
};
