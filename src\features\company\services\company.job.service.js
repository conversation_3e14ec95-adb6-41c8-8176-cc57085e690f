/**
 * Company Job Service
 * 
 * Handles company-specific job operations and API calls
 * Includes job creation, management, and application handling
 */

import { supabase } from '@/utils/supabaseClient';
import cacheService, { CACHE_TTL } from '@/utils/cacheService';

// Company job cache keys
const COMPANY_JOB_CACHE_KEYS = {
  JOBS: 'company_jobs',
  JOB_DETAIL: 'company_job_detail',
  APPLICATIONS: 'company_job_applications',
};

/**
 * Company Job Service for job-related operations
 */
const companyJobService = {
  /**
   * Create a new job posting
   * @param {string} companyId - Company ID
   * @param {Object} jobData - Job data
   * @returns {Promise<Object>} - Created job
   */
  async createJob(companyId, jobData) {
    try {
      const data = {
        ...jobData,
        company_id: companyId,
        status: jobData.status || 'draft',
        created_at: new Date().toISOString(),
      };

      const { data: result, error } = await supabase
        .from('jobs')
        .insert(data)
        .select(`
          *,
          companies:company_id (
            id,
            company_name,
            company_logo_url
          )
        `)
        .single();

      if (error) throw error;

      // Clear company jobs cache
      this.clearJobCache(companyId);

      return { success: true, data: result };
    } catch (error) {
      console.error('Error creating job:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Update a job posting
   * @param {string} jobId - Job ID
   * @param {string} companyId - Company ID (for verification)
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} - Updated job
   */
  async updateJob(jobId, companyId, updates) {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('jobs')
        .update(updateData)
        .eq('id', jobId)
        .eq('company_id', companyId) // Ensure company can only update their own jobs
        .select(`
          *,
          companies:company_id (
            id,
            company_name,
            company_logo_url
          )
        `)
        .single();

      if (error) throw error;

      // Clear relevant caches
      this.clearJobCache(companyId);
      this.clearJobDetailCache(jobId);

      return { success: true, data };
    } catch (error) {
      console.error('Error updating job:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Delete a job posting
   * @param {string} jobId - Job ID
   * @param {string} companyId - Company ID (for verification)
   * @returns {Promise<Object>} - Result of the operation
   */
  async deleteJob(jobId, companyId) {
    try {
      // Check if job has applications
      const { data: applications } = await supabase
        .from('applications')
        .select('id')
        .eq('job_id', jobId)
        .limit(1);

      if (applications && applications.length > 0) {
        return { 
          success: false, 
          error: 'Cannot delete job with existing applications. Please close the job instead.' 
        };
      }

      const { data, error } = await supabase
        .from('jobs')
        .delete()
        .eq('id', jobId)
        .eq('company_id', companyId) // Ensure company can only delete their own jobs
        .select()
        .single();

      if (error) throw error;

      // Clear relevant caches
      this.clearJobCache(companyId);
      this.clearJobDetailCache(jobId);

      return { success: true, data };
    } catch (error) {
      console.error('Error deleting job:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get job applications for a specific job
   * @param {string} jobId - Job ID
   * @param {string} companyId - Company ID (for verification)
   * @param {Object} filters - Optional filters
   * @returns {Promise<Object>} - Job applications
   */
  async getJobApplications(jobId, companyId, filters = {}) {
    try {
      // Verify job belongs to company
      const { data: job } = await supabase
        .from('jobs')
        .select('id')
        .eq('id', jobId)
        .eq('company_id', companyId)
        .single();

      if (!job) {
        return { success: false, error: 'Job not found or access denied' };
      }

      let query = supabase
        .from('applications')
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name, mobile_number),
            candidate_profiles:id (
              resume_url,
              years_experience,
              current_job_title,
              current_company,
              profile_photo_url,
              skills,
              expected_ctc,
              notice_period
            )
          )
        `)
        .eq('job_id', jobId);

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.experience_min) {
        query = query.gte('candidates.candidate_profiles.years_experience', filters.experience_min);
      }

      if (filters.experience_max) {
        query = query.lte('candidates.candidate_profiles.years_experience', filters.experience_max);
      }

      const { data, error } = await query
        .order('application_date', { ascending: false });

      if (error) throw error;

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error fetching job applications:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Update application status
   * @param {string} applicationId - Application ID
   * @param {string} companyId - Company ID (for verification)
   * @param {string} status - New status
   * @param {Object} additionalData - Additional data to update
   * @returns {Promise<Object>} - Result of the operation
   */
  async updateApplicationStatus(applicationId, companyId, status, additionalData = {}) {
    try {
      // Verify application belongs to company job
      const { data: application } = await supabase
        .from('applications')
        .select(`
          id,
          jobs:job_id (company_id)
        `)
        .eq('id', applicationId)
        .single();

      if (!application || application.jobs.company_id !== companyId) {
        return { success: false, error: 'Application not found or access denied' };
      }

      const updateData = {
        status,
        ...additionalData,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('applications')
        .update(updateData)
        .eq('id', applicationId)
        .select(`
          *,
          candidates:candidate_id (
            id,
            profiles:id (email, full_name),
            candidate_profiles:id (
              current_job_title,
              profile_photo_url
            )
          )
        `)
        .single();

      if (error) throw error;

      // Clear company applications cache
      this.clearApplicationsCache(companyId);

      return { success: true, data };
    } catch (error) {
      console.error('Error updating application status:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get job posting analytics
   * @param {string} jobId - Job ID
   * @param {string} companyId - Company ID (for verification)
   * @returns {Promise<Object>} - Job analytics
   */
  async getJobAnalytics(jobId, companyId) {
    try {
      // Verify job belongs to company
      const { data: job } = await supabase
        .from('jobs')
        .select('id, created_at, status')
        .eq('id', jobId)
        .eq('company_id', companyId)
        .single();

      if (!job) {
        return { success: false, error: 'Job not found or access denied' };
      }

      // Get application statistics
      const { data: applications, error: appsError } = await supabase
        .from('applications')
        .select('status, application_date, interview_score')
        .eq('job_id', jobId);

      if (appsError) throw appsError;

      // Get interview statistics
      const { data: interviews, error: interviewsError } = await supabase
        .from('interviews')
        .select('status, interview_date, score')
        .eq('job_id', jobId);

      if (interviewsError) throw interviewsError;

      const analytics = {
        job: {
          id: job.id,
          status: job.status,
          daysActive: Math.ceil((new Date() - new Date(job.created_at)) / (1000 * 60 * 60 * 24)),
        },
        applications: {
          total: applications.length,
          pending: applications.filter(a => a.status === 'applied').length,
          reviewed: applications.filter(a => a.status === 'reviewed').length,
          interviewed: applications.filter(a => a.status === 'interviewed').length,
          hired: applications.filter(a => a.status === 'hired').length,
          rejected: applications.filter(a => a.status === 'rejected').length,
          averageScore: 0,
        },
        interviews: {
          total: interviews.length,
          scheduled: interviews.filter(i => i.status === 'scheduled').length,
          completed: interviews.filter(i => i.status === 'completed').length,
          cancelled: interviews.filter(i => i.status === 'cancelled').length,
          averageScore: 0,
        },
        trends: {
          applicationsPerDay: applications.length / Math.max(analytics.job.daysActive, 1),
          interviewsPerDay: interviews.length / Math.max(analytics.job.daysActive, 1),
        }
      };

      // Calculate average application scores
      const scoredApplications = applications.filter(a => a.interview_score);
      if (scoredApplications.length > 0) {
        const totalScore = scoredApplications.reduce((sum, a) => sum + a.interview_score, 0);
        analytics.applications.averageScore = Math.round((totalScore / scoredApplications.length) * 10) / 10;
      }

      // Calculate average interview scores
      const scoredInterviews = interviews.filter(i => i.score);
      if (scoredInterviews.length > 0) {
        const totalScore = scoredInterviews.reduce((sum, i) => sum + i.score, 0);
        analytics.interviews.averageScore = Math.round((totalScore / scoredInterviews.length) * 10) / 10;
      }

      return { success: true, data: analytics };
    } catch (error) {
      console.error('Error fetching job analytics:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Bulk update job status
   * @param {Array} jobIds - Array of job IDs
   * @param {string} companyId - Company ID (for verification)
   * @param {string} status - New status
   * @returns {Promise<Object>} - Result of the operation
   */
  async bulkUpdateJobStatus(jobIds, companyId, status) {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .update({ 
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('company_id', companyId) // Ensure company can only update their own jobs
        .in('id', jobIds)
        .select('id, title, status');

      if (error) throw error;

      // Clear company jobs cache
      this.clearJobCache(companyId);

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error bulk updating job status:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Clear job cache for a company
   * @param {string} companyId - Company ID
   */
  clearJobCache(companyId) {
    if (!companyId) return;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_JOB_CACHE_KEYS.JOBS, companyId);
    cacheService.del(cacheKey);
  },

  /**
   * Clear job detail cache
   * @param {string} jobId - Job ID
   */
  clearJobDetailCache(jobId) {
    if (!jobId) return;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_JOB_CACHE_KEYS.JOB_DETAIL, jobId);
    cacheService.del(cacheKey);
  },

  /**
   * Clear applications cache for a company
   * @param {string} companyId - Company ID
   */
  clearApplicationsCache(companyId) {
    if (!companyId) return;
    
    const cacheKey = cacheService.getCacheKey(COMPANY_JOB_CACHE_KEYS.APPLICATIONS, companyId);
    cacheService.del(cacheKey);
  }
};

export default companyJobService;
