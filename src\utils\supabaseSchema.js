/**
 * Supabase Database Schema - OPTIMIZED VERSION
 *
 * This file defines the optimized database schema for the real estate job platform.
 * Email and phone are now stored centrally in the profiles table to eliminate redundancy.
 */

// Table: profiles (extends Supabase Auth)
// Now stores email, phone, and role centrally to eliminate redundancy
export const profilesSchema = {
  id: 'uuid references auth.users(id)', // Primary key linking to Supabase Auth
  username: 'text',
  email: 'text not null unique', // ✅ Centralized email storage
  phone_number: 'text not null unique', // ✅ Centralized phone storage
  email_verified: 'boolean default false', // ✅ Email verification status
  phone_verified: 'boolean default false', // ✅ Phone verification status (future)
  role: "text not null check (role in ('candidate', 'company', 'interviewer'))",
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};

// Table: candidate_profiles
// Contains ONLY role-specific information (no email/phone redundancy)
export const candidateProfilesSchema = {
  id: 'uuid references profiles(id)', // Foreign key to profiles table
  full_name: 'text not null',
  // ❌ Removed: phone_number (now in profiles table)
  // ❌ Removed: email (now in profiles table)
  city: 'text not null',
  role_applied_for: 'text not null',
  years_experience: 'integer not null',
  resume_url: 'text',
  current_job_title: 'text',
  current_company: 'text',
  skills: 'jsonb', // Array of skills with categories
  expected_ctc: 'text',
  notice_period: 'text',
  linkedin_url: 'text',
  languages: 'jsonb', // Array of languages with proficiency levels
  profile_photo_url: 'text',
  video_intro_url: 'text',
  availability_calendar: 'jsonb', // Calendar for interview availability
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};

// Table: company_profiles
// Contains ONLY role-specific information (no email/phone redundancy)
export const companyProfilesSchema = {
  id: 'uuid references profiles(id)', // Foreign key to profiles table
  company_name: 'text not null',
  primary_recruiter_name: 'text not null',
  // ❌ Removed: corporate_email (now in profiles table as email)
  // ❌ Removed: phone_number (now in profiles table)
  company_logo_url: 'text',
  website_url: 'text',
  office_locations: 'jsonb', // Array of office locations
  company_type: 'text', // Developer, Brokerage, etc.
  company_size: 'text', // Employee count range
  company_description: 'text',
  chat_notification_settings: 'jsonb',
  interview_preferences: 'jsonb',
  billing_information: 'jsonb',
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};

// Table: interviewer_profiles
// Contains ONLY role-specific information (no email/phone redundancy)
export const interviewerProfilesSchema = {
  id: 'uuid references profiles(id)', // Foreign key to profiles table
  full_name: 'text not null',
  // ❌ Removed: professional_email (now in profiles table as email)
  // ❌ Removed: phone_number (now in profiles table)
  current_designation: 'text',
  current_company: 'text',
  linkedin_url: 'text',
  years_experience: 'integer',
  preferred_interview_roles: 'jsonb', // Array of roles they can interview for
  equipment_confirmation: 'boolean default false',
  payment_details: 'jsonb',
  performance_rating: 'numeric default 0',
  availability_calendar: 'jsonb', // Calendar for interview availability
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};

// ===========================================
// DATABASE VIEWS (for easy data access)
// ===========================================

// These views combine profiles table with role-specific tables
// Use these in your application instead of joining tables manually

export const databaseViews = {
  // Complete candidate profile view
  candidate_profiles_complete: 'Combines profiles + candidate_profiles',

  // Complete company profile view
  company_profiles_complete: 'Combines profiles + company_profiles',

  // Complete interviewer profile view
  interviewer_profiles_complete: 'Combines profiles + interviewer_profiles',
};

// ===========================================
// UTILITY FUNCTIONS (available in database)
// ===========================================

export const utilityFunctions = {
  // Email validation and verification
  check_email_exists_anywhere: 'Function to check email uniqueness in profiles table',
  check_incomplete_registration: 'Function to check for unverified users in auth.users',
  cleanup_incomplete_registration: 'Function to remove incomplete registrations',

  // Email verification functions
  mark_email_verified: 'Function to mark user email as verified',
  is_email_verified: 'Function to check if user email is verified',

  // Profile management
  create_profile: 'Function to create user profile (fallback)',

  // System functions (used by triggers)
  handle_new_user_registration: 'Trigger function for new user creation',
  handle_updated_at: 'Trigger function to update timestamps',
  sync_email_from_auth: 'Trigger function to sync email from auth.users',
};

// ===========================================
// USAGE EXAMPLES
// ===========================================

export const usageExamples = {
  // Fetch complete candidate profile
  fetchCandidateProfile: `
    const { data } = await supabase
      .from('candidate_profiles_complete')
      .select('*')
      .eq('id', userId)
      .single();
  `,

  // Check email exists
  checkEmailExists: `
    const { data } = await supabase
      .rpc('check_email_exists_anywhere', { email_to_check: email });
  `,

  // Check incomplete registration
  checkIncompleteRegistration: `
    const { data } = await supabase
      .rpc('check_incomplete_registration', { email_to_check: email });
  `,

  // Cleanup incomplete registration
  cleanupIncompleteRegistration: `
    const { data } = await supabase
      .rpc('cleanup_incomplete_registration', { email_to_cleanup: email });
  `,

  // Mark email verified
  markEmailVerified: `
    const { data } = await supabase
      .rpc('mark_email_verified', { user_id: userId });
  `,

  // Update profile (two-step process)
  updateProfile: `
    // Step 1: Update profiles table
    await supabase.from('profiles').update({ email, phone_number }).eq('id', userId);

    // Step 2: Update role-specific table
    await supabase.from('candidate_profiles').update({ city, skills }).eq('id', userId);
  `,

  // Email verification examples
  checkEmailVerified: `
    const { data: isVerified } = await supabase
      .rpc('is_email_verified', { user_id: userId });
  `,

  markEmailVerified: `
    const { data: success } = await supabase
      .rpc('mark_email_verified', { user_id: userId });
  `,

  // Email verification guard usage
  emailVerificationGuard: `
    import EmailVerificationGuard from '@/components/EmailVerificationGuard';

    // Wrap protected routes
    <EmailVerificationGuard>
      <Dashboard />
    </EmailVerificationGuard>
  `,
};

// Table: jobs
// Contains information about job postings
export const jobsSchema = {
  id: 'uuid default uuid_generate_v4() primary key',
  company_id: 'uuid references company_profiles(id)',
  title: 'text not null',
  description: 'text not null',
  required_skills: 'jsonb', // Array of required skills
  experience_level: 'text not null',
  location: 'text not null',
  salary_range: 'jsonb', // Min and max salary
  status: "text default 'active'", // active, filled, closed
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};

// Table: interviews
// Contains information about interviews
export const interviewsSchema = {
  id: 'uuid default uuid_generate_v4() primary key',
  candidate_id: 'uuid references candidate_profiles(id)',
  interviewer_id: 'uuid references interviewer_profiles(id)',
  job_id: 'uuid references jobs(id)',
  company_id: 'uuid references company_profiles(id)',
  status: 'text not null', // requested, scheduled, completed
  score: 'numeric',
  feedback: 'text',
  interview_date: 'timestamp with time zone',
  duration_minutes: 'integer',
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};

// Table: applications
// Contains information about job applications
export const applicationsSchema = {
  id: 'uuid default uuid_generate_v4() primary key',
  candidate_id: 'uuid references candidate_profiles(id)',
  job_id: 'uuid references jobs(id)',
  status: 'text not null', // applied, shortlisted, interviewed, hired, rejected
  application_date: 'timestamp with time zone default now()',
  interview_score: 'numeric',
  created_at: 'timestamp with time zone default now()',
  updated_at: 'timestamp with time zone default now()',
};
