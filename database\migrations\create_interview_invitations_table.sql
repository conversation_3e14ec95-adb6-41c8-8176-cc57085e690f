-- Create interview_invitations table for managing interview invitation links
-- This table stores secure invitation tokens and tracks invitation status

CREATE TABLE IF NOT EXISTS interview_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Interview reference
  interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE,
  
  -- Invitation details
  invitation_token TEXT NOT NULL UNIQUE,
  invited_by UUID NOT NULL REFERENCES profiles(id),
  invited_role TEXT NOT NULL CHECK (invited_role IN ('candidate', 'interviewer', 'company')),
  
  -- Recipient information
  recipient_email TEXT NOT NULL,
  recipient_name TEXT NOT NULL,
  
  -- Interview scheduling
  scheduled_date TIMESTAMPTZ NOT NULL,
  duration_minutes INTEGER NOT NULL DEFAULT 60,
  interview_type TEXT NOT NULL DEFAULT 'video' CHECK (interview_type IN ('video', 'phone', 'in-person')),
  
  -- Invitation message
  message TEXT,
  
  -- Status tracking
  status TEXT NOT NULL DEFAULT 'sent' CHECK (status IN ('sent', 'accepted', 'declined', 'expired')),
  
  -- Response tracking
  accepted_by UUID REFERENCES profiles(id),
  accepted_at TIMESTAMPTZ,
  declined_by UUID REFERENCES profiles(id),
  declined_at TIMESTAMPTZ,
  decline_reason TEXT,
  
  -- Expiration
  expires_at TIMESTAMPTZ NOT NULL,
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_interview_invitations_token ON interview_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_interview_invitations_interview_id ON interview_invitations(interview_id);
CREATE INDEX IF NOT EXISTS idx_interview_invitations_recipient_email ON interview_invitations(recipient_email);
CREATE INDEX IF NOT EXISTS idx_interview_invitations_status ON interview_invitations(status);
CREATE INDEX IF NOT EXISTS idx_interview_invitations_expires_at ON interview_invitations(expires_at);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_interview_invitations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_interview_invitations_updated_at
  BEFORE UPDATE ON interview_invitations
  FOR EACH ROW
  EXECUTE FUNCTION update_interview_invitations_updated_at();

-- Add columns to existing interviews table for invitation support
ALTER TABLE interviews 
ADD COLUMN IF NOT EXISTS invitation_token TEXT,
ADD COLUMN IF NOT EXISTS meeting_link TEXT;

-- Create index for invitation token in interviews table
CREATE INDEX IF NOT EXISTS idx_interviews_invitation_token ON interviews(invitation_token);

-- Enable Row Level Security
ALTER TABLE interview_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for interview_invitations table

-- Policy: Users can view invitations they created or are invited to
CREATE POLICY "Users can view their invitations" ON interview_invitations
  FOR SELECT USING (
    invited_by = auth.uid() OR 
    recipient_email = (SELECT email FROM profiles WHERE id = auth.uid())
  );

-- Policy: Users can create invitations for interviews they're part of
CREATE POLICY "Users can create invitations" ON interview_invitations
  FOR INSERT WITH CHECK (
    invited_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM interviews i 
      WHERE i.id = interview_id AND (
        i.candidate_id = auth.uid() OR 
        i.interviewer_id = auth.uid() OR 
        i.company_id = auth.uid()
      )
    )
  );

-- Policy: Users can update invitations they created or are invited to
CREATE POLICY "Users can update their invitations" ON interview_invitations
  FOR UPDATE USING (
    invited_by = auth.uid() OR 
    recipient_email = (SELECT email FROM profiles WHERE id = auth.uid())
  );

-- Policy: Users can delete invitations they created
CREATE POLICY "Users can delete their invitations" ON interview_invitations
  FOR DELETE USING (invited_by = auth.uid());

-- Grant permissions
GRANT ALL ON interview_invitations TO authenticated;
GRANT USAGE ON SEQUENCE interview_invitations_id_seq TO authenticated;

-- Create function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS void AS $$
BEGIN
  UPDATE interview_invitations 
  SET status = 'expired' 
  WHERE expires_at < NOW() AND status = 'sent';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up expired invitations (if pg_cron is available)
-- This would need to be run manually or via a cron job if pg_cron is not available
-- SELECT cron.schedule('cleanup-expired-invitations', '0 * * * *', 'SELECT cleanup_expired_invitations();');

COMMENT ON TABLE interview_invitations IS 'Stores interview invitation links and tracks their status';
COMMENT ON COLUMN interview_invitations.invitation_token IS 'Secure token for accessing the interview room';
COMMENT ON COLUMN interview_invitations.invited_role IS 'Role of the person creating the invitation';
COMMENT ON COLUMN interview_invitations.interview_type IS 'Type of interview: video, phone, or in-person';
COMMENT ON COLUMN interview_invitations.status IS 'Current status of the invitation';
COMMENT ON COLUMN interview_invitations.expires_at IS 'When the invitation expires (24 hours from creation)';

-- Sample data for testing (optional)
-- INSERT INTO interview_invitations (
--   interview_id,
--   invitation_token,
--   invited_by,
--   invited_role,
--   recipient_email,
--   recipient_name,
--   scheduled_date,
--   duration_minutes,
--   interview_type,
--   message,
--   expires_at
-- ) VALUES (
--   'sample-interview-id',
--   'sample-secure-token',
--   'sample-user-id',
--   'interviewer',
--   '<EMAIL>',
--   'John Doe',
--   NOW() + INTERVAL '1 day',
--   60,
--   'video',
--   'Looking forward to our interview!',
--   NOW() + INTERVAL '24 hours'
-- );
