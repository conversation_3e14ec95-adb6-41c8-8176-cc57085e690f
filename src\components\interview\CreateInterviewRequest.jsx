/**
 * CreateInterviewRequest Component
 *
 * Modal component for candidates to create interview requests
 * These requests will be visible to all interviewers until accepted
 */

import { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Button,
  Space,
  Typography,
  Alert,
  message,
  TimePicker,
  Card,
  Row,
  Col,
} from 'antd';
import { CalendarOutlined, ClockCircleOutlined, MessageOutlined } from '@ant-design/icons';
import { createInterviewRequest } from '@/services/interview.service';
import useAuth from '@/hooks/useAuth';
import { useJobs } from '@/features/candidate/hooks';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CreateInterviewRequest = ({ visible, onCancel, onSuccess, defaultJobId = null }) => {
  const [form] = Form.useForm();
  const { user, profile } = useAuth();
  const { jobs } = useJobs();
  const [loading, setLoading] = useState(false);
  const [availableJobs, setAvailableJobs] = useState([]);

  // Load available jobs for interview requests
  useEffect(() => {
    if (jobs && jobs.length > 0) {
      // Filter jobs that candidate has applied to or can apply to
      const eligibleJobs = jobs.filter(
        (job) => job.status === 'active' && (!job.isApplied || job.isApplied) // Include both applied and non-applied jobs
      );
      setAvailableJobs(eligibleJobs);
    }
  }, [jobs]);

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      // Combine date and time
      const interviewDateTime = values.preferredDate
        .hour(values.preferredTime.hour())
        .minute(values.preferredTime.minute());

      const requestData = {
        candidate_id: user.id,
        job_id: values.jobId,
        company_id: values.companyId, // Will be set based on selected job
        preferred_date: interviewDateTime.toISOString(),
        duration_minutes: values.duration || 60,
        interview_type: values.interviewType || 'video',
        message: values.message,
        status: 'requested',
      };

      // Get company ID from selected job
      const selectedJob = availableJobs.find((job) => job.id === values.jobId);
      if (selectedJob) {
        requestData.company_id = selectedJob.company_id;
      }

      const result = await createInterviewRequest(requestData);

      if (result.success) {
        message.success('Interview request created successfully! Interviewers will be notified.');
        form.resetFields();
        if (onSuccess) {
          onSuccess(result.data);
        }
        onCancel();
      } else {
        message.error(result.error || 'Failed to create interview request');
      }
    } catch (error) {
      console.error('Error creating interview request:', error);
      message.error('Failed to create interview request');
    } finally {
      setLoading(false);
    }
  };

  // Handle job selection change
  const handleJobChange = (jobId) => {
    const selectedJob = availableJobs.find((job) => job.id === jobId);
    if (selectedJob) {
      form.setFieldsValue({
        companyId: selectedJob.company_id,
      });
    }
  };

  // Reset form and close modal
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // Get time slots for selection
  const getTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 17; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = dayjs().hour(hour).minute(minute);
        slots.push({
          value: time,
          label: time.format('HH:mm'),
        });
      }
    }
    return slots;
  };

  return (
    <Modal
      title={
        <Space>
          <CalendarOutlined />
          Request Interview
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={700}
      destroyOnClose
    >
      <Alert
        message="Request an Interview"
        description="Submit your interview request and available interviewers will be notified. Once an interviewer accepts, you'll receive a confirmation."
        type="info"
        showIcon
        className="mb-6"
      />

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          jobId: defaultJobId,
          duration: 60,
          interviewType: 'video',
          preferredTime: dayjs().hour(10).minute(0),
        }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="jobId"
              label="Select Position"
              rules={[{ required: true, message: 'Please select a position' }]}
            >
              <Select
                placeholder="Choose the position you want to interview for"
                onChange={handleJobChange}
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {availableJobs.map((job) => (
                  <Option
                    key={job.id}
                    value={job.id}
                  >
                    <Space>
                      <MessageOutlined />
                      <span>{job.title}</span>
                      <Text type="secondary">at {job.companies?.company_name}</Text>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="preferredDate"
              label="Preferred Date"
              rules={[{ required: true, message: 'Please select preferred date' }]}
            >
              <DatePicker
                className="w-full"
                disabledDate={(current) => current && current < dayjs().startOf('day')}
                placeholder="Select date"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="preferredTime"
              label="Preferred Time"
              rules={[{ required: true, message: 'Please select preferred time' }]}
            >
              <TimePicker
                className="w-full"
                format="HH:mm"
                minuteStep={30}
                placeholder="Select time"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="duration"
              label="Expected Duration"
              rules={[{ required: true, message: 'Please select duration' }]}
            >
              <Select placeholder="Select duration">
                <Option value={30}>30 minutes</Option>
                <Option value={45}>45 minutes</Option>
                <Option value={60}>1 hour</Option>
                <Option value={90}>1.5 hours</Option>
                <Option value={120}>2 hours</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="interviewType"
              label="Interview Type"
              rules={[{ required: true, message: 'Please select interview type' }]}
            >
              <Select placeholder="Select interview type">
                <Option value="video">Video Call</Option>
                <Option value="phone">Phone Call</Option>
                <Option value="in-person">In-Person</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="message"
          label="Message to Interviewer (Optional)"
        >
          <TextArea
            rows={3}
            placeholder="Add any specific requirements, topics you'd like to discuss, or questions you have..."
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Card className="bg-gray-50 dark:bg-gray-800 mb-6">
          <Title level={5}>What happens next?</Title>
          <ul className="mb-0 pl-4">
            <li>Your request will be visible to all available interviewers</li>
            <li>Once an interviewer accepts, you'll receive a notification</li>
            <li>The interview will appear in your "Upcoming" tab</li>
            <li>You'll receive a calendar invitation with meeting details</li>
          </ul>
        </Card>

        <Form.Item className="mb-0">
          <Space className="w-full justify-end">
            <Button onClick={handleCancel}>Cancel</Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<CalendarOutlined />}
            >
              Submit Request
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateInterviewRequest;
