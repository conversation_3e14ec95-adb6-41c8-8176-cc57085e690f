import React, { useState, useEffect } from 'react';
import { Card, Typography, Spin, message } from 'antd';
import Calender from '@/components/shared/Calendar';
import { getCalendarEvents } from '@/services/calendar.service';
import useAuth from '@/hooks/useAuth';

const { Title } = Typography;

const Calendar = () => {
  const { user, profile } = useAuth();
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load calendar events
  useEffect(() => {
    if (user && profile) {
      loadCalendarEvents();
    }
  }, [user, profile]);

  const loadCalendarEvents = async () => {
    try {
      setLoading(true);
      const result = await getCalendarEvents(user.id, profile.role);

      if (result.success) {
        setEvents(result.data);
      } else {
        message.error('Failed to load calendar events');
      }
    } catch (error) {
      console.error('Error loading calendar events:', error);
      message.error('Failed to load calendar events');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEvent = (eventData) => {
    // Handle adding new events (if needed)
    console.log('Add event:', eventData);
  };

  const handleEditEvent = (eventData) => {
    // Handle editing events (if needed)
    console.log('Edit event:', eventData);
  };

  const handleDeleteEvent = (eventId) => {
    // Handle deleting events (if needed)
    console.log('Delete event:', eventId);
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Title
        level={2}
        className="mb-6"
      >
        Interview Calendar
      </Title>

      <Calender
        events={events}
        onAddEvent={handleAddEvent}
        onEditEvent={handleEditEvent}
        onDeleteEvent={handleDeleteEvent}
        userType={profile?.role || 'interviewer'}
        loading={loading}
      />
    </div>
  );
};

export default Calendar;
