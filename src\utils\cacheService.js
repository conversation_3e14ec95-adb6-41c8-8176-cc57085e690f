/**
 * Cache Service
 * 
 * A lightweight caching layer using browser storage
 * Provides efficient data caching without external dependencies
 */

// Cache TTLs (in seconds)
const CACHE_TTL = {
  PROFILE: 5 * 60, // 5 minutes
  JOBS: 2 * 60, // 2 minutes
  APPLICATIONS: 1 * 60, // 1 minute
  INTERVIEWS: 1 * 60, // 1 minute
  ASSESSMENTS: 5 * 60, // 5 minutes
};

// Cache key prefixes
const CACHE_KEYS = {
  PROFILE: 'profile',
  JOBS: 'jobs',
  SAVED_JOBS: 'saved_jobs',
  APPLICATIONS: 'applications',
  INTERVIEWS: 'interviews',
  ASSESSMENTS: 'assessments',
};

class CacheService {
  constructor(namespace = 'flyt', storage = localStorage) {
    this.namespace = namespace;
    this.storage = storage;
  }

  /**
   * Generate a namespaced key
   * @param {string} key - Original key
   * @returns {string} - Namespaced key
   */
  getNamespacedKey(key) {
    return `${this.namespace}:${key}`;
  }

  /**
   * Set a value in the cache with optional expiration
   * @param {string} key - Cache key
   * @param {any} value - Value to store
   * @param {number} ttl - Time to live in seconds (optional)
   * @returns {boolean} - Success status
   */
  set(key, value, ttl = null) {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      
      // Prepare cache item
      const item = {
        value,
        created: Date.now(),
        ttl: ttl ? ttl * 1000 : null, // Convert to milliseconds
      };

      // Serialize
      const serialized = JSON.stringify(item);
      
      // Store the item
      this.storage.setItem(namespacedKey, serialized);
      
      return true;
    } catch (error) {
      console.error('Error setting cache item:', error);
      return false;
    }
  }

  /**
   * Get a value from the cache
   * @param {string} key - Cache key
   * @returns {any|null} - Cached value or null if not found/expired
   */
  get(key) {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      const serialized = this.storage.getItem(namespacedKey);
      
      if (!serialized) return null;
      
      const item = JSON.parse(serialized);
      
      // Check if item has expired
      if (item.ttl && Date.now() > item.created + item.ttl) {
        this.del(key);
        return null;
      }
      
      return item.value;
    } catch (error) {
      console.error('Error getting cache item:', error);
      return null;
    }
  }

  /**
   * Delete a value from the cache
   * @param {string} key - Cache key
   * @returns {boolean} - Success status
   */
  del(key) {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      this.storage.removeItem(namespacedKey);
      return true;
    } catch (error) {
      console.error('Error deleting cache item:', error);
      return false;
    }
  }

  /**
   * Check if a key exists in the cache
   * @param {string} key - Cache key
   * @returns {boolean} - Whether the key exists and is not expired
   */
  exists(key) {
    return this.get(key) !== null;
  }

  /**
   * Set expiration time for an existing key
   * @param {string} key - Cache key
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} - Success status
   */
  expire(key, ttl) {
    try {
      const value = this.get(key);
      if (value === null) return false;
      
      return this.set(key, value, ttl);
    } catch (error) {
      console.error('Error setting expiration:', error);
      return false;
    }
  }

  /**
   * Get all keys matching a pattern
   * @param {string} pattern - Pattern to match (simple wildcard support)
   * @returns {string[]} - Array of matching keys
   */
  keys(pattern = '*') {
    try {
      const keys = [];
      const prefix = this.namespace + ':';
      const regex = new RegExp('^' + prefix + pattern.replace(/\*/g, '.*') + '$');
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (regex.test(key)) {
          keys.push(key.substring(prefix.length));
        }
      }
      
      return keys;
    } catch (error) {
      console.error('Error getting keys:', error);
      return [];
    }
  }

  /**
   * Clear all items in the cache for this namespace
   * @returns {boolean} - Success status
   */
  flushAll() {
    try {
      const keys = this.keys('*');
      keys.forEach(key => this.del(key));
      return true;
    } catch (error) {
      console.error('Error flushing cache:', error);
      return false;
    }
  }

  /**
   * Clean expired items from the cache
   * @returns {number} - Number of items removed
   */
  cleanExpiredItems() {
    try {
      let removed = 0;
      const keys = this.keys('*');
      
      keys.forEach(key => {
        const namespacedKey = this.getNamespacedKey(key);
        const serialized = this.storage.getItem(namespacedKey);
        
        if (serialized) {
          const item = JSON.parse(serialized);
          if (item.ttl && Date.now() > item.created + item.ttl) {
            this.del(key);
            removed++;
          }
        }
      });
      
      return removed;
    } catch (error) {
      console.error('Error cleaning expired items:', error);
      return 0;
    }
  }

  /**
   * Generate a cache key with user ID
   * @param {string} prefix - Cache key prefix
   * @param {string} userId - User ID
   * @param {string} suffix - Optional suffix
   * @returns {string} - Cache key
   */
  getCacheKey(prefix, userId, suffix = '') {
    return `${prefix}:${userId}${suffix ? `:${suffix}` : ''}`;
  }
}

// Create singleton instance
const cacheService = new CacheService();

// Export constants and service
export { CACHE_TTL, CACHE_KEYS };
export default cacheService;
