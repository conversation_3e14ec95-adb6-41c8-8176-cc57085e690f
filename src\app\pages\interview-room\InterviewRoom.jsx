/**
 * Interview Room Component
 * 
 * Main interview room where candidates and interviewers can join
 * Provides video call interface and interview management features
 */

import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Avatar,
  Typography,
  Space,
  Tag,
  Alert,
  Spin,
  Modal,
  message,
  Row,
  Col,
  Divider,
} from 'antd';
import {
  VideoCameraOutlined,
  AudioOutlined,
  PhoneOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  getInterviewLink,
  joinInterviewRoom,
  endInterviewSession,
  validateInterviewAccess,
} from '@/services/interviewLink.service';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;

const InterviewRoom = () => {
  const { linkId } = useParams();
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  
  const [interviewData, setInterviewData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [isJoined, setIsJoined] = useState(false);
  const [videoEnabled, setVideoEnabled] = useState(true);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [interviewStarted, setInterviewStarted] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(null);
  
  const videoRef = useRef(null);
  const streamRef = useRef(null);

  // Load interview data and validate access
  useEffect(() => {
    if (user && linkId) {
      loadInterviewData();
    }
  }, [user, linkId]);

  // Timer for interview duration
  useEffect(() => {
    if (interviewStarted && interviewData) {
      const duration = interviewData.duration_minutes * 60; // Convert to seconds
      setTimeRemaining(duration);
      
      const timer = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            handleInterviewEnd();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [interviewStarted, interviewData]);

  const loadInterviewData = async () => {
    try {
      setLoading(true);
      
      // Validate access first
      const accessResult = await validateInterviewAccess(linkId, user.id);
      if (!accessResult.success) {
        setError(accessResult.error);
        return;
      }

      setInterviewData(accessResult.data);
      setUserRole(accessResult.data.user_role);
    } catch (err) {
      console.error('Error loading interview data:', err);
      setError('Failed to load interview data');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinInterview = async () => {
    try {
      setLoading(true);
      
      const joinResult = await joinInterviewRoom(linkId, user.id, userRole);
      if (!joinResult.success) {
        message.error(joinResult.error);
        return;
      }

      // Initialize media devices
      await initializeMedia();
      setIsJoined(true);
      setInterviewStarted(true);
      message.success('Joined interview successfully!');
    } catch (err) {
      console.error('Error joining interview:', err);
      message.error('Failed to join interview');
    } finally {
      setLoading(false);
    }
  };

  const initializeMedia = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: videoEnabled,
        audio: audioEnabled,
      });
      
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error('Error accessing media devices:', err);
      message.error('Failed to access camera/microphone');
    }
  };

  const toggleVideo = () => {
    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setVideoEnabled(videoTrack.enabled);
      }
    }
  };

  const toggleAudio = () => {
    if (streamRef.current) {
      const audioTrack = streamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setAudioEnabled(audioTrack.enabled);
      }
    }
  };

  const handleInterviewEnd = () => {
    Modal.confirm({
      title: 'End Interview',
      content: 'Are you sure you want to end this interview?',
      icon: <ExclamationCircleOutlined />,
      onOk: async () => {
        try {
          await endInterviewSession(linkId, user.id);
          
          // Stop media streams
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
          }
          
          message.success('Interview ended successfully');
          navigate(`/${profile?.role}/dashboard`);
        } catch (err) {
          console.error('Error ending interview:', err);
          message.error('Failed to end interview');
        }
      },
    });
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getOtherParticipant = () => {
    if (!interviewData) return null;
    
    if (userRole === 'candidate') {
      return {
        name: interviewData.interviewer_profiles?.full_name,
        photo: interviewData.interviewer_profiles?.profile_photo_url,
        role: 'Interviewer',
      };
    } else {
      return {
        name: interviewData.candidate_profiles?.full_name,
        photo: interviewData.candidate_profiles?.profile_photo_url,
        role: 'Candidate',
      };
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" tip="Loading interview room..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="max-w-md">
          <Alert
            message="Access Denied"
            description={error}
            type="error"
            showIcon
            action={
              <Button onClick={() => navigate('/')}>
                Go Home
              </Button>
            }
          />
        </Card>
      </div>
    );
  }

  const otherParticipant = getOtherParticipant();

  return (
    <div className="interview-room min-h-screen bg-gray-100 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <Card className="mb-4">
          <Row gutter={24} align="middle">
            <Col flex="auto">
              <Space direction="vertical" size={0}>
                <Title level={3} className="mb-0">
                  {interviewData.jobs?.title} Interview
                </Title>
                <Text type="secondary">
                  {interviewData.company_profiles?.company_name}
                </Text>
              </Space>
            </Col>
            <Col>
              <Space>
                <Tag color="blue">
                  <CalendarOutlined /> {dayjs(interviewData.interview_date).format('MMM DD, YYYY')}
                </Tag>
                {timeRemaining !== null && (
                  <Tag color={timeRemaining < 300 ? 'red' : 'green'}>
                    <ClockCircleOutlined /> {formatTime(timeRemaining)}
                  </Tag>
                )}
              </Space>
            </Col>
          </Row>
        </Card>

        {!isJoined ? (
          /* Pre-join Screen */
          <Card>
            <div className="text-center">
              <Title level={2}>Ready to join your interview?</Title>
              
              {otherParticipant && (
                <div className="mb-6">
                  <Avatar
                    size={80}
                    src={otherParticipant.photo}
                    icon={<UserOutlined />}
                    className="mb-2"
                  />
                  <div>
                    <Text strong className="text-lg">{otherParticipant.name}</Text>
                    <br />
                    <Text type="secondary">{otherParticipant.role}</Text>
                  </div>
                </div>
              )}

              <div className="mb-6">
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  className="w-64 h-48 bg-gray-200 rounded-lg mx-auto"
                />
              </div>

              <Space size="large" className="mb-6">
                <Button
                  type={videoEnabled ? 'primary' : 'default'}
                  icon={<VideoCameraOutlined />}
                  onClick={toggleVideo}
                >
                  {videoEnabled ? 'Camera On' : 'Camera Off'}
                </Button>
                <Button
                  type={audioEnabled ? 'primary' : 'default'}
                  icon={<AudioOutlined />}
                  onClick={toggleAudio}
                >
                  {audioEnabled ? 'Mic On' : 'Mic Off'}
                </Button>
              </Space>

              <div>
                <Button
                  type="primary"
                  size="large"
                  onClick={handleJoinInterview}
                  loading={loading}
                >
                  Join Interview
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          /* Interview Screen */
          <Row gutter={16}>
            <Col span={18}>
              <Card className="h-96">
                <div className="relative h-full">
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    className="w-full h-full object-cover rounded"
                  />
                  
                  {/* Controls Overlay */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                    <Space>
                      <Button
                        type={videoEnabled ? 'primary' : 'default'}
                        shape="circle"
                        icon={<VideoCameraOutlined />}
                        onClick={toggleVideo}
                      />
                      <Button
                        type={audioEnabled ? 'primary' : 'default'}
                        shape="circle"
                        icon={<AudioOutlined />}
                        onClick={toggleAudio}
                      />
                      <Button
                        danger
                        shape="circle"
                        icon={<PhoneOutlined />}
                        onClick={handleInterviewEnd}
                      />
                    </Space>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col span={6}>
              <Card title="Interview Details" className="mb-4">
                <Space direction="vertical" className="w-full">
                  <div>
                    <Text strong>Position:</Text>
                    <br />
                    <Text>{interviewData.jobs?.title}</Text>
                  </div>
                  <div>
                    <Text strong>Duration:</Text>
                    <br />
                    <Text>{interviewData.duration_minutes} minutes</Text>
                  </div>
                  {otherParticipant && (
                    <div>
                      <Text strong>{otherParticipant.role}:</Text>
                      <br />
                      <Space>
                        <Avatar
                          src={otherParticipant.photo}
                          icon={<UserOutlined />}
                        />
                        <Text>{otherParticipant.name}</Text>
                      </Space>
                    </div>
                  )}
                </Space>
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </div>
  );
};

export default InterviewRoom;
