import { Suspense } from 'react';
import { createBrowserRouter } from 'react-router-dom';
import PageLoader from '@/components/shared/PageLoader';
import {
  publicRoutes,
  authRoutes,
  candidateRoutes,
  companyRoutes,
  interviewerRoutes,
  errorRoutes,
  interviewRoomRoute,
  globalInterviewRoomRoute,
} from './routes';
import MainLayout from '@/app/layouts/MainLayout';
import CandidateLayout from '@/app/layouts/CandidateLayout';
import CompanyLayout from '@/app/layouts/CompanyLayout';
import InterviewerLayout from '@/app/layouts/InterviewerLayout';
import ProtectedRoute from '@/router/ProtectedRoute';

// Suspense Wrapper Component
function SuspenseWrapper({ Component, message = 'Loading' }) {
  return (
    <Suspense fallback={<PageLoader message={message} />}>
      <Component />
    </Suspense>
  );
}

// Helper to transform route definitions
const transformRoutes = (routes) => {
  return routes.map((route) => ({
    ...route,
    element: (
      <SuspenseWrapper
        Component={route.component}
        message={'Preparing your dashboard'}
      />
    ),
  }));
};

// Create the router configuration
const AppRouter = createBrowserRouter([
  // Public routes with MainLayout
  {
    path: '/',
    element: <MainLayout />,
    children: [
      // Home route (index)
      {
        index: true,
        element: (
          <SuspenseWrapper
            Component={publicRoutes[0].component}
            isPublicRoutes={true}
          />
        ),
      },
      // Other public routes
      ...transformRoutes(publicRoutes.slice(1)),
      // Public auth routes
      ...transformRoutes(authRoutes),
      // Error routes for public layout
      ...transformRoutes(errorRoutes),
    ],
  },

  // Candidate protected routes
  {
    path: '/candidate',
    element: <ProtectedRoute allowedRoles={['candidate']} />,
    children: [
      {
        element: <CandidateLayout />,
        children: [
          ...transformRoutes(candidateRoutes),
          {
            ...interviewRoomRoute,
            element: (
              <SuspenseWrapper
                Component={interviewRoomRoute.component}
                message="Loading interview room..."
              />
            ),
          },
          // Error routes for candidate layout
          ...transformRoutes(errorRoutes),
        ],
      },
    ],
  },

  // Company protected routes
  {
    path: '/org',
    element: <ProtectedRoute allowedRoles={['company']} />,
    children: [
      {
        element: <CompanyLayout />,
        children: [
          ...transformRoutes(companyRoutes),
          {
            ...interviewRoomRoute,
            element: (
              <SuspenseWrapper
                Component={interviewRoomRoute.component}
                message="Loading interview room..."
              />
            ),
          },
          // Error routes for company layout
          ...transformRoutes(errorRoutes),
        ],
      },
    ],
  },

  // Interviewer protected routes
  {
    path: '/sourcer',
    element: <ProtectedRoute allowedRoles={['interviewer']} />,
    children: [
      {
        element: <InterviewerLayout />,
        children: [
          ...transformRoutes(interviewerRoutes),
          {
            ...interviewRoomRoute,
            element: (
              <SuspenseWrapper
                Component={interviewRoomRoute.component}
                message="Loading interview room..."
              />
            ),
          },
          // Error routes for interviewer layout
          ...transformRoutes(errorRoutes),
        ],
      },
    ],
  },

  // Global Interview Room Route (accessible by all authenticated users)
  {
    ...globalInterviewRoomRoute,
    element: (
      <SuspenseWrapper
        Component={globalInterviewRoomRoute.component}
        message="Loading interview room..."
      />
    ),
  },
]);

export default AppRouter;
