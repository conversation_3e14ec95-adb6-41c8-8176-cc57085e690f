import { useEffect, useState } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';

const useCandidates = () => {
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(false);
  const { user, profile } = useAuth();

  const fetchCandidates = async () => {
    if (!user || !profile) return;

    setLoading(true);
    try {
      // Get all interviews conducted by this interviewer
      const { data: interviewData, error: interviewError } = await supabase
        .from('interviews')
        .select('candidate_id')
        .eq('interviewer_id', profile.id);

      if (interviewError) throw interviewError;

      // Get unique candidate IDs
      const candidateIds = [...new Set(interviewData.map((item) => item.candidate_id))];

      if (candidateIds.length === 0) {
        setCandidates([]);
        return [];
      }

      // Fetch candidate profiles
      const { data: candidateData, error: candidateError } = await supabase
        .from('candidate_profiles')
        .select('*')
        .in('id', candidateIds);

      if (candidateError) throw candidateError;

      setCandidates(candidateData);
      return candidateData;
    } catch (error) {
      console.error('Failed to fetch candidates', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user && profile) {
      fetchCandidates();
    }
  }, [user, profile]);

  return {
    candidates,
    loading,
    refetch: fetchCandidates,
  };
};

export default useCandidates;
