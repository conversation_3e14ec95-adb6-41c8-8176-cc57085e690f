import React from 'react';
import { Card, Typography, Spin } from 'antd';
import { Pie } from '@ant-design/plots';
import { useColorModeStore } from '@/store/colorMode.store';

const { Title, Text } = Typography;

const PieChart = ({ title, data, loading = false, height = 300 }) => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const config = {
    appendPadding: 10,
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    innerRadius: 0.6,
    label: {
      type: 'inner',
      offset: '-30%',
      content: ({ percent }) => `${(percent * 100).toFixed(0)}%`,
      style: {
        fontSize: 14,
        textAlign: 'center',
        fill: '#fff',
      },
    },
    color: ['#1890FF', '#91D5FF'],
    statistic: {
      title: {
        style: {
          fontSize: '14px',
          lineHeight: 1.2,
          color: isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.45)',
        },
        content: 'Total',
      },
      content: {
        style: {
          fontSize: '20px',
          color: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        },
        content: data.reduce((sum, item) => sum + item.value, 0),
      },
    },
    legend: {
      position: 'bottom',
      itemName: {
        style: {
          fill: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        },
      },
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.type, value: datum.value };
      },
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <Card
      className="chart-card h-full transition-all hover:shadow-md"
      style={{
        borderRadius: '12px',
        borderColor: 'transparent',
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.04)' : 'white',
      }}
      styles={{ body: { padding: '16px' } }}
    >
      <div className="flex items-center justify-between mb-4">
        <Title
          level={5}
          className="m-0"
        >
          {title}
        </Title>
      </div>

      <div style={{ height: `${height}px`, position: 'relative' }}>
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <Spin />
          </div>
        ) : (
          <Pie {...config} />
        )}
      </div>
    </Card>
  );
};

export default PieChart;
