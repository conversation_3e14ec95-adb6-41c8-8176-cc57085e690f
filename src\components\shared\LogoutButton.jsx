import React, { useState } from 'react';
import { Button, Modal } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import useLogout from '@/hooks/useLogout';

/**
 * A reusable logout button component that can be used throughout the app
 * Includes confirmation modal and loading state
 */
const LogoutButton = ({
  type = 'primary',
  size = 'middle',
  danger = false,
  block = false,
  icon = <LogoutOutlined />,
  text = 'Logout',
  redirectTo = '/login',
  className = '',
  showConfirmation = true,
  forceReload = false,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { handleLogout, isLoggingOut } = useLogout({
    redirectTo,
    forceReload,
    showToast: true,
  });

  const showLogoutConfirmation = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const onLogoutConfirm = async () => {
    setIsModalVisible(false);
    await handleLogout();
  };

  const onButtonClick = () => {
    if (showConfirmation) {
      showLogoutConfirmation();
    } else {
      handleLogout();
    }
  };

  return (
    <>
      <Button
        type={type}
        danger={danger}
        icon={icon}
        size={size}
        block={block}
        onClick={onButtonClick}
        loading={isLoggingOut && !showConfirmation}
        className={className}
      >
        {text}
      </Button>

      {showConfirmation && (
        <Modal
          title="Confirm Logout"
          open={isModalVisible}
          onOk={onLogoutConfirm}
          onCancel={handleCancel}
          okText="Logout"
          cancelText="Cancel"
          okButtonProps={{
            loading: isLoggingOut,
            danger: true,
          }}
        >
          <p>Are you sure you want to logout?</p>
        </Modal>
      )}
    </>
  );
};

export default LogoutButton;
