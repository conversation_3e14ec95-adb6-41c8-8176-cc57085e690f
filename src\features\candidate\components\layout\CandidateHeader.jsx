import React from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Input } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Briefcase,
  FileText,
  Video,
  User,
  Bell,
  MenuIcon,
  Search,
  MessageSquare,
} from 'lucide-react';
import useDeviceDetect from '@/hooks/useDeviceDetect';
import ColorModeToggle from '@/components/shared/ColorModeToggle';

const { Header } = Layout;
const { Search: SearchInput } = Input;

/**
 * Header component for the Candidate Layout
 *
 * @param {Object} props
 * @param {Object} props.user - Current user object
 * @param {Object} props.profile - Candidate profile object
 * @param {Array} props.notifications - Notifications array
 * @param {Function} props.setMobileDrawerOpen - Function to toggle mobile drawer
 * @param {Function} props.setSearchModalVisible - Function to toggle search modal
 * @param {Array} props.userMenuItems - User menu dropdown items
 * @param {Array} props.notificationItems - Notification dropdown items
 * @param {boolean} props.isDark - Dark mode flag
 * @param {string} props.logo_dark - Dark mode logo path
 * @param {string} props.logo_lite - Light mode logo path
 */
const CandidateHeader = ({
  user,
  profile,
  notifications,
  setMobileDrawerOpen,
  setSearchModalVisible,
  userMenuItems,
  notificationItems,
  isDark,
  logo_dark,
  logo_lite,
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isMobile } = useDeviceDetect();

  // Get current page from URL
  const currentPage = location.pathname.split('/')[2] || 'dashboard';

  return (
    <Header
      className="bg-white dark:bg-gray-800 shadow-md px-2 sm:px-4 py-0 flex items-center justify-between h-16 sticky top-0 z-50 transition-all"
      style={{
        minWidth: isMobile ? '100%' : '900px',
        width: '100%',
      }}
    >
      <div className="flex items-center flex-shrink-0">
        <Link
          to="/candidate/dashboard"
          className="mr-4 sm:mr-8 flex-shrink-0"
        >
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="InterviewPro"
            className="h-6 sm:h-8 transition-all hover:opacity-80"
          />
        </Link>

        {!isMobile && (
          <div className="flex-grow overflow-hidden">
            <Menu
              mode="horizontal"
              selectedKeys={[currentPage]}
              className="border-0 bg-transparent"
              overflowedIndicator={null}
              disabledOverflow={true}
              style={{
                fontSize: '14px',
                fontWeight: 500,
                minWidth: '600px',
                flex: '1 1 auto',
              }}
              items={[
                {
                  key: 'dashboard',
                  icon: <LayoutDashboard size={16} />,
                  label: 'Home',
                  className: 'hover:text-primary transition-colors px-4',
                  onClick: () => navigate('/candidate/dashboard'),
                },
                {
                  key: 'jobs',
                  icon: <Briefcase size={16} />,
                  label: 'Jobs',
                  className: 'hover:text-primary transition-colors px-4',
                  onClick: () => navigate('/candidate/jobs'),
                },
                {
                  key: 'applications',
                  icon: <FileText size={16} />,
                  label: 'Applications',
                  className: 'hover:text-primary transition-colors px-4',
                  onClick: () => navigate('/candidate/applications'),
                },
                {
                  key: 'interviews',
                  icon: <Video size={16} />,
                  label: 'Interviews',
                  className: 'hover:text-primary transition-colors px-4',
                  onClick: () => navigate('/candidate/interviews'),
                },
                {
                  key: 'messages',
                  icon: <MessageSquare size={16} />,
                  label: 'Messages',
                  className: 'hover:text-primary transition-colors px-4',
                  onClick: () => navigate('/candidate/messages'),
                },
              ]}
            />
          </div>
        )}
      </div>

      <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0 ml-2 sm:ml-4">
        {/* Mobile Search Button */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={18} />}
            onClick={() => setSearchModalVisible(true)}
            className="hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          />
        )}

        {/* Desktop Search */}
        {!isMobile && (
          <SearchInput
            placeholder="Search jobs, companies..."
            style={{ width: 250 }}
            prefix={<Search size={16} />}
            onClick={() => setSearchModalVisible(true)}
            readOnly
            className="rounded-full border-gray-200 dark:border-gray-700 hover:border-primary dark:hover:border-primary transition-colors"
          />
        )}

        {/* Theme toggle */}
        {!isMobile && (
          <ColorModeToggle
            type="icon"
            size="middle"
            className="mr-1"
          />
        )}

        <Dropdown
          menu={{ items: notificationItems }}
          placement="bottomRight"
          trigger={['click']}
        >
          <Badge
            count={notifications.filter((n) => !n.read).length}
            size="small"
            className="cursor-pointer"
          >
            <Button
              type="text"
              icon={<Bell size={20} />}
              className="flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            />
          </Badge>
        </Dropdown>

        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          trigger={['click']}
        >
          <div className="flex items-center cursor-pointer hover:opacity-80 transition-opacity">
            <Avatar
              src={profile?.profile_photo_url}
              icon={!profile?.profile_photo_url && <User />}
              size={32}
              className="border-2 border-gray-200 dark:border-gray-700"
            />
            {!isMobile && (
              <span className="ml-2 text-sm font-medium">
                {profile?.full_name || user?.email?.split('@')[0] || 'User'}
              </span>
            )}
          </div>
        </Dropdown>

        {isMobile && (
          <Button
            type="text"
            icon={<MenuIcon size={20} />}
            onClick={() => setMobileDrawerOpen(true)}
            className="hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          />
        )}
      </div>
    </Header>
  );
};

export default CandidateHeader;
