import React, { useState, useEffect } from 'react';
import {
  Calendar as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  TimePicker,
  Row,
  Col,
  Typography,
  Tooltip,
  Popover,
  Tabs,
  List,
  Tag,
  Space,
  Divider,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  TeamOutlined,
  VideoCameraOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * Reusable Calendar component that can be used for different user types
 * @param {Object} props
 * @param {Array} props.events - Events to display on the calendar
 * @param {Function} props.onAddEvent - Function to handle adding a new event
 * @param {Function} props.onEditEvent - Function to handle editing an event
 * @param {Function} props.onDeleteEvent - Function to handle deleting an event
 * @param {Array} props.eventTypes - Types of events that can be created
 * @param {Array} props.participants - Potential participants to select
 * @param {boolean} props.loading - Loading state
 * @param {string} props.userType - Type of user (candidate, recruiter, company)
 * @param {Object} props.viewOptions - Options for different calendar views
 */
const Calendar = ({
  events = [],
  onAddEvent,
  onEditEvent,
  onDeleteEvent,
  eventTypes = [],
  participants = [],
  loading = false,
  userType = 'candidate',
  viewOptions = { month: true, week: true, day: true, agenda: true },
}) => {
  const { isMobile, isTablet } = useDeviceDetect();
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [modalVisible, setModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [calendarView, setCalendarView] = useState('month');
  const [form] = Form.useForm();
  const [showAllDayEvents, setShowAllDayEvents] = useState(false);

  // Default event types if none provided
  const defaultEventTypes = [
    { id: 'interview', name: 'Interview', color: 'blue' },
    { id: 'meeting', name: 'Meeting', color: 'green' },
    { id: 'deadline', name: 'Deadline', color: 'red' },
    { id: 'reminder', name: 'Reminder', color: 'orange' },
  ];

  const displayEventTypes = eventTypes.length > 0 ? eventTypes : defaultEventTypes;

  // Get events for a specific date
  const getEventsForDate = (date) => {
    return events.filter(
      (event) => dayjs(event.date).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
    );
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);

    // If in day view, don't need to change the view
    if (calendarView !== 'day') {
      setCalendarView('day');
    }
  };

  // Handle date cell render
  const dateCellRender = (date) => {
    const dateEvents = getEventsForDate(date);

    return (
      <ul className="events-list p-0 m-0 list-none">
        {dateEvents.slice(0, 3).map((event) => (
          <li
            key={event.id}
            onClick={(e) => {
              e.stopPropagation();
              handleEventClick(event);
            }}
          >
            <Badge
              color={
                event.type ? displayEventTypes.find((t) => t.id === event.type)?.color : 'blue'
              }
              text={
                <Tooltip title={event.title}>
                  <span
                    className="truncate block"
                    style={{ maxWidth: '100%' }}
                  >
                    {isMobile
                      ? event.title.length > 6
                        ? `${event.title.substring(0, 6)}...`
                        : event.title
                      : event.title.length > 12
                        ? `${event.title.substring(0, 12)}...`
                        : event.title}
                  </span>
                </Tooltip>
              }
            />
          </li>
        ))}
        {dateEvents.length > 3 && (
          <li className="text-xs text-gray-500 mt-1">+{dateEvents.length - 3} more</li>
        )}
      </ul>
    );
  };

  // Handle month cell render
  const monthCellRender = (date) => {
    const monthEvents = events.filter(
      (event) =>
        dayjs(event.date).month() === date.month() && dayjs(event.date).year() === date.year()
    );

    return (
      <div className="month-cell">
        <div className="text-right mb-2">
          <Text type="secondary">{monthEvents.length} events</Text>
        </div>
      </div>
    );
  };

  // Handle event click
  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setModalMode('edit');

    form.setFieldsValue({
      title: event.title,
      description: event.description,
      type: event.type,
      date: dayjs(event.date),
      time: dayjs(event.time),
      duration: event.duration,
      location: event.location,
      participants: event.participants,
      isOnline: event.isOnline,
      meetingLink: event.meetingLink,
    });

    setModalVisible(true);
  };

  // Handle add event button click
  const handleAddEvent = () => {
    setModalMode('add');
    setSelectedEvent(null);

    form.setFieldsValue({
      title: '',
      description: '',
      type: displayEventTypes[0]?.id,
      date: selectedDate,
      time: dayjs().hour(9).minute(0),
      duration: 60,
      location: '',
      participants: [],
      isOnline: true,
      meetingLink: '',
    });

    setModalVisible(true);
  };

  // Handle form submission
  const handleFormSubmit = (values) => {
    const eventData = {
      ...values,
      date: values.date.format('YYYY-MM-DD'),
      time: values.time.format('HH:mm'),
    };

    if (modalMode === 'add') {
      if (onAddEvent) {
        onAddEvent(eventData);
      }
    } else {
      if (onEditEvent && selectedEvent) {
        onEditEvent({ ...selectedEvent, ...eventData });
      }
    }

    setModalVisible(false);
    form.resetFields();
  };

  // Handle event deletion
  const handleDeleteEvent = () => {
    if (onDeleteEvent && selectedEvent) {
      onDeleteEvent(selectedEvent.id);
    }

    setModalVisible(false);
    form.resetFields();
  };

  // Render event details
  const renderEventDetails = (event) => {
    const eventType = displayEventTypes.find((t) => t.id === event.type);

    return (
      <div className="event-details">
        <div className="flex items-center mb-4">
          <Badge color={eventType?.color || 'blue'} />
          <Text
            strong
            className="ml-2"
          >
            {eventType?.name || 'Event'}
          </Text>
        </div>

        <Title level={4}>{event.title}</Title>

        <div className="event-info mt-4">
          <div className="flex items-start mb-3">
            <CalendarOutlined className="mt-1 mr-2" />
            <div>
              <div>{dayjs(event.date).format('dddd, MMMM D, YYYY')}</div>
              <div className="flex items-center mt-1">
                <ClockCircleOutlined className="mr-2" />
                <span>{dayjs(event.time, 'HH:mm').format('h:mm A')}</span>
                <span className="mx-2">•</span>
                <span>{event.duration} minutes</span>
              </div>
            </div>
          </div>

          {event.location && (
            <div className="flex items-start mb-3">
              <EnvironmentOutlined className="mt-1 mr-2" />
              <div>
                {event.isOnline ? (
                  <>
                    <div>Online Meeting</div>
                    {event.meetingLink && (
                      <a
                        href={event.meetingLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary"
                      >
                        Join Meeting
                      </a>
                    )}
                  </>
                ) : (
                  <div>{event.location}</div>
                )}
              </div>
            </div>
          )}

          {event.participants && event.participants.length > 0 && (
            <div className="flex items-start mb-3">
              <TeamOutlined className="mt-1 mr-2" />
              <div>
                <div className="mb-1">Participants:</div>
                <div className="flex flex-wrap gap-1">
                  {event.participants.map((participant) => (
                    <Tag key={participant}>{participant}</Tag>
                  ))}
                </div>
              </div>
            </div>
          )}

          {event.description && (
            <div className="flex items-start mb-3">
              <InfoCircleOutlined className="mt-1 mr-2" />
              <div>
                <div className="mb-1">Description:</div>
                <Paragraph>{event.description}</Paragraph>
              </div>
            </div>
          )}
        </div>

        <Divider />

        <div className="flex justify-end">
          <Space>
            <Button
              icon={<DeleteOutlined />}
              danger
              onClick={handleDeleteEvent}
            >
              Delete
            </Button>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => {
                setModalMode('edit');
                form.setFieldsValue({
                  title: event.title,
                  description: event.description,
                  type: event.type,
                  date: dayjs(event.date),
                  time: dayjs(event.time, 'HH:mm'),
                  duration: event.duration,
                  location: event.location,
                  participants: event.participants,
                  isOnline: event.isOnline,
                  meetingLink: event.meetingLink,
                });
              }}
            >
              Edit
            </Button>
          </Space>
        </div>
      </div>
    );
  };

  // Render day view
  const renderDayView = () => {
    const dayEvents = getEventsForDate(selectedDate);

    return (
      <div className="day-view">
        <Title
          level={4}
          className="mb-4"
        >
          {selectedDate.format('dddd, MMMM D, YYYY')}
        </Title>

        {dayEvents.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={dayEvents.sort((a, b) =>
              dayjs(a.time, 'HH:mm').diff(dayjs(b.time, 'HH:mm'))
            )}
            renderItem={(event) => {
              const eventType = displayEventTypes.find((t) => t.id === event.type);

              return (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => handleEventClick(event)}
                    >
                      View
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge
                        color={eventType?.color || 'blue'}
                        style={{ width: '10px', height: '10px' }}
                      />
                    }
                    title={
                      <div className="flex items-center">
                        <span className="mr-3">{dayjs(event.time, 'HH:mm').format('h:mm A')}</span>
                        <span>{event.title}</span>
                      </div>
                    }
                    description={
                      <div className="flex items-center text-xs">
                        <span>{event.duration} min</span>
                        {event.location && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{event.isOnline ? 'Online Meeting' : event.location}</span>
                          </>
                        )}
                        {event.participants && event.participants.length > 0 && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{event.participants.length} participants</span>
                          </>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
        ) : (
          <div className="text-center py-8">
            <Text type="secondary">No events scheduled for this day</Text>
            <div className="mt-4">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddEvent}
              >
                Add Event
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render agenda view
  const renderAgendaView = () => {
    // Group events by date
    const groupedEvents = events.reduce((acc, event) => {
      const dateKey = dayjs(event.date).format('YYYY-MM-DD');
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(event);
      return acc;
    }, {});

    // Sort dates
    const sortedDates = Object.keys(groupedEvents).sort((a, b) => dayjs(a).diff(dayjs(b)));

    return (
      <div className="agenda-view">
        <Title
          level={4}
          className="mb-4"
        >
          Upcoming Events
        </Title>

        {sortedDates.length > 0 ? (
          sortedDates.map((dateKey) => (
            <div
              key={dateKey}
              className="mb-6"
            >
              <div className="date-header mb-3 pb-2 border-b">
                <Text strong>{dayjs(dateKey).format('dddd, MMMM D, YYYY')}</Text>
              </div>

              <List
                itemLayout="horizontal"
                dataSource={groupedEvents[dateKey].sort((a, b) =>
                  dayjs(a.time, 'HH:mm').diff(dayjs(b.time, 'HH:mm'))
                )}
                renderItem={(event) => {
                  const eventType = displayEventTypes.find((t) => t.id === event.type);

                  return (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          onClick={() => handleEventClick(event)}
                        >
                          View
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Badge
                            color={eventType?.color || 'blue'}
                            style={{ width: '10px', height: '10px' }}
                          />
                        }
                        title={
                          <div className="flex items-center">
                            <span className="mr-3">
                              {dayjs(event.time, 'HH:mm').format('h:mm A')}
                            </span>
                            <span>{event.title}</span>
                          </div>
                        }
                        description={
                          <div className="flex items-center text-xs">
                            <span>{event.duration} min</span>
                            {event.location && (
                              <>
                                <span className="mx-2">•</span>
                                <span>{event.isOnline ? 'Online Meeting' : event.location}</span>
                              </>
                            )}
                          </div>
                        }
                      />
                    </List.Item>
                  );
                }}
              />
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <Text type="secondary">No upcoming events</Text>
            <div className="mt-4">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddEvent}
              >
                Add Event
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="calendar-component">
      <Card
        bordered={false}
        className="mb-4"
        title={
          <div className="flex justify-between items-center">
            <Title
              level={4}
              className="m-0"
            >
              Calendar
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddEvent}
            >
              {isMobile ? 'Add' : 'Add Event'}
            </Button>
          </div>
        }
        extra={
          <Space>
            {viewOptions.month && (
              <Button
                type={calendarView === 'month' ? 'primary' : 'default'}
                onClick={() => setCalendarView('month')}
              >
                Month
              </Button>
            )}
            {viewOptions.week && (
              <Button
                type={calendarView === 'week' ? 'primary' : 'default'}
                onClick={() => setCalendarView('week')}
              >
                Week
              </Button>
            )}
            {viewOptions.day && (
              <Button
                type={calendarView === 'day' ? 'primary' : 'default'}
                onClick={() => setCalendarView('day')}
              >
                Day
              </Button>
            )}
            {viewOptions.agenda && (
              <Button
                type={calendarView === 'agenda' ? 'primary' : 'default'}
                onClick={() => setCalendarView('agenda')}
              >
                Agenda
              </Button>
            )}
          </Space>
        }
      >
        {calendarView === 'month' && (
          <AntCalendar
            dateCellRender={dateCellRender}
            monthCellRender={monthCellRender}
            onSelect={handleDateSelect}
            value={selectedDate}
          />
        )}

        {calendarView === 'week' && (
          <AntCalendar
            dateCellRender={dateCellRender}
            onSelect={handleDateSelect}
            value={selectedDate}
            mode="week"
          />
        )}

        {calendarView === 'day' && renderDayView()}

        {calendarView === 'agenda' && renderAgendaView()}
      </Card>

      <Modal
        title={modalMode === 'add' ? 'Add Event' : 'Edit Event'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        {modalMode === 'edit' && selectedEvent ? (
          <Tabs defaultActiveKey="details">
            <TabPane
              tab="Details"
              key="details"
            >
              {renderEventDetails(selectedEvent)}
            </TabPane>
            <TabPane
              tab="Edit"
              key="edit"
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleFormSubmit}
              >
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item
                      name="title"
                      label="Title"
                      rules={[{ required: true, message: 'Please enter event title' }]}
                    >
                      <Input
                        placeholder="Enter event title"
                        maxLength={100}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item
                      name="description"
                      label="Description"
                    >
                      <Input.TextArea
                        placeholder="Enter event description"
                        maxLength={500}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item
                      name="type"
                      label="Event Type"
                      rules={[{ required: true, message: 'Please select event type' }]}
                    >
                      <Select
                        placeholder="Select event type"
                        showSearch
                        filterOption={(input, option) =>
                          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                      >
                        {displayEventTypes.map((eventType) => (
                          <Option
                            key={eventType.id}
                            value={eventType.id}
                          >
                            <Badge color={eventType.color} />
                            <span className="ml-2">{eventType.name}</span>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="date"
                      label="Date"
                      rules={[{ required: true, message: 'Please select event date' }]}
                    >
                      <DatePicker
                        placeholder="Select event date"
                        format="YYYY-MM-DD"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="time"
                      label="Time"
                      rules={[{ required: true, message: 'Please select event time' }]}
                    >
                      <TimePicker
                        placeholder="Select event time"
                        format="h:mm A"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="duration"
                      label="Duration (minutes)"
                      rules={[{ required: true, message: 'Please enter event duration' }]}
                    >
                      <Input
                        type="number"
                        min={1}
                        placeholder="Enter event duration in minutes"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="location"
                      label="Location"
                    >
                      <Input
                        placeholder="Enter event location"
                        maxLength={100}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="isOnline"
                      label="Is Online Event"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  {form.getFieldValue('isOnline') && (
                    <Col span={24}>
                      <Form.Item
                        name="meetingLink"
                        label="Meeting Link"
                        rules={[{ required: true, message: 'Please enter meeting link' }]}
                      >
                        <Input
                          placeholder="Enter meeting link"
                          maxLength={200}
                        />
                      </Form.Item>
                    </Col>
                  )}
                  <Col span={24}>
                    <Form.Item
                      name="participants"
                      label="Participants"
                    >
                      <Select
                        mode="tags"
                        placeholder="Select participants"
                        style={{ width: '100%' }}
                        showSearch
                        filterOption={(input, option) =>
                          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                      >
                        {participants.map((participant) => (
                          <Option
                            key={participant}
                            value={participant}
                          >
                            {participant}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                  >
                    {modalMode === 'add' ? 'Add Event' : 'Save Changes'}
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>
          </Tabs>
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="title"
                  label="Title"
                  rules={[{ required: true, message: 'Please enter event title' }]}
                >
                  <Input
                    placeholder="Enter event title"
                    maxLength={100}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  name="description"
                  label="Description"
                >
                  <Input.TextArea
                    placeholder="Enter event description"
                    maxLength={500}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  name="type"
                  label="Event Type"
                  rules={[{ required: true, message: 'Please select event type' }]}
                >
                  <Select
                    placeholder="Select event type"
                    showSearch
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {displayEventTypes.map((eventType) => (
                      <Option
                        key={eventType.id}
                        value={eventType.id}
                      >
                        <Badge color={eventType.color} />
                        <span className="ml-2">{eventType.name}</span>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="date"
                  label="Date"
                  rules={[{ required: true, message: 'Please select event date' }]}
                >
                  <DatePicker
                    placeholder="Select event date"
                    format="YYYY-MM-DD"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="time"
                  label="Time"
                  rules={[{ required: true, message: 'Please select event time' }]}
                >
                  <TimePicker
                    placeholder="Select event time"
                    format="h:mm A"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="duration"
                  label="Duration (minutes)"
                  rules={[{ required: true, message: 'Please enter event duration' }]}
                >
                  <Input
                    type="number"
                    min={1}
                    placeholder="Enter event duration in minutes"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="location"
                  label="Location"
                >
                  <Input
                    placeholder="Enter event location"
                    maxLength={100}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="isOnline"
                  label="Is Online Event"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              {form.getFieldValue('isOnline') && (
                <Col span={24}>
                  <Form.Item
                    name="meetingLink"
                    label="Meeting Link"
                    rules={[{ required: true, message: 'Please enter meeting link' }]}
                  >
                    <Input
                      placeholder="Enter meeting link"
                      maxLength={200}
                    />
                  </Form.Item>
                </Col>
              )}
              <Col span={24}>
                <Form.Item
                  name="participants"
                  label="Participants"
                >
                  <Select
                    mode="tags"
                    placeholder="Select participants"
                    style={{ width: '100%' }}
                    showSearch
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {participants.map((participant) => (
                      <Option
                        key={participant}
                        value={participant}
                      >
                        {participant}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
              >
                {modalMode === 'add' ? 'Add Event' : 'Save Changes'}
              </Button>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default Calendar;
