/**
 * useVideoCall Hook
 * 
 * React hook for managing video call functionality
 * Provides state management and event handlers for video interviews
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import VideoCallService from '@/services/videoCall.service';
import { supabase } from '@/utils/supabaseClient';
import { message } from 'antd';

const useVideoCall = (interviewId, userId, userRole) => {
  // Video call states
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [connectionState, setConnectionState] = useState('new');
  const [error, setError] = useState(null);

  // Chat states
  const [chatMessages, setChatMessages] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isChatOpen, setIsChatOpen] = useState(false);

  // Call statistics
  const [callDuration, setCallDuration] = useState(0);
  const [callQuality, setCallQuality] = useState('good');

  // Refs
  const videoCallService = useRef(null);
  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);
  const callStartTime = useRef(null);
  const durationInterval = useRef(null);
  const signalingChannel = useRef(null);

  // Initialize video call service
  useEffect(() => {
    videoCallService.current = new VideoCallService();

    // Set up event handlers
    videoCallService.current.onRemoteStream = (stream) => {
      if (remoteVideoRef.current) {
        remoteVideoRef.current.srcObject = stream;
      }
    };

    videoCallService.current.onConnectionStateChange = (state) => {
      setConnectionState(state);
      setIsConnected(state === 'connected');
      
      if (state === 'connected' && !callStartTime.current) {
        callStartTime.current = Date.now();
        startDurationTimer();
      }
    };

    videoCallService.current.onDataChannelMessage = (data) => {
      if (data.type === 'chat') {
        setChatMessages(prev => [...prev, {
          id: Date.now(),
          message: data.message,
          timestamp: data.timestamp,
          sender: 'remote',
        }]);
        
        if (!isChatOpen) {
          setUnreadCount(prev => prev + 1);
        }
      }
    };

    return () => {
      if (videoCallService.current) {
        videoCallService.current.endCall();
      }
      stopDurationTimer();
    };
  }, []);

  // Set up signaling channel using Supabase realtime
  useEffect(() => {
    if (!interviewId) return;

    const channel = supabase.channel(`interview-${interviewId}`)
      .on('broadcast', { event: 'signaling' }, (payload) => {
        handleSignalingMessage(payload.payload);
      })
      .subscribe();

    signalingChannel.current = channel;

    return () => {
      if (signalingChannel.current) {
        signalingChannel.current.unsubscribe();
      }
    };
  }, [interviewId]);

  // Start duration timer
  const startDurationTimer = useCallback(() => {
    durationInterval.current = setInterval(() => {
      if (callStartTime.current) {
        setCallDuration(Math.floor((Date.now() - callStartTime.current) / 1000));
      }
    }, 1000);
  }, []);

  // Stop duration timer
  const stopDurationTimer = useCallback(() => {
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
      durationInterval.current = null;
    }
  }, []);

  // Handle signaling messages
  const handleSignalingMessage = useCallback(async (message) => {
    try {
      switch (message.type) {
        case 'offer':
          const answer = await videoCallService.current.createAnswer(message.offer);
          sendSignalingMessage({ type: 'answer', answer });
          break;
        case 'answer':
          await videoCallService.current.handleAnswer(message.answer);
          break;
        case 'ice-candidate':
          await videoCallService.current.handleIceCandidate(message.candidate);
          break;
        default:
          console.log('Unknown signaling message:', message);
      }
    } catch (error) {
      console.error('Error handling signaling message:', error);
      setError(error.message);
    }
  }, []);

  // Send signaling message
  const sendSignalingMessage = useCallback((message) => {
    if (signalingChannel.current) {
      signalingChannel.current.send({
        type: 'broadcast',
        event: 'signaling',
        payload: { ...message, from: userId, role: userRole },
      });
    }
  }, [userId, userRole]);

  // Initialize call
  const initializeCall = useCallback(async (constraints = { video: true, audio: true }) => {
    try {
      setIsConnecting(true);
      setError(null);

      const stream = await videoCallService.current.initializeMedia(constraints);
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }

      setIsCallActive(true);
      return stream;
    } catch (error) {
      console.error('Error initializing call:', error);
      setError(error.message);
      message.error('Failed to access camera/microphone');
    } finally {
      setIsConnecting(false);
    }
  }, []);

  // Start call (caller)
  const startCall = useCallback(async () => {
    try {
      const offer = await videoCallService.current.createOffer();
      sendSignalingMessage({ type: 'offer', offer });
    } catch (error) {
      console.error('Error starting call:', error);
      setError(error.message);
    }
  }, [sendSignalingMessage]);

  // End call
  const endCall = useCallback(() => {
    videoCallService.current.endCall();
    setIsCallActive(false);
    setIsConnected(false);
    setConnectionState('closed');
    stopDurationTimer();
    callStartTime.current = null;
    setCallDuration(0);
  }, [stopDurationTimer]);

  // Toggle audio
  const toggleAudio = useCallback(() => {
    const muted = videoCallService.current.toggleAudio();
    setIsMuted(muted);
    return muted;
  }, []);

  // Toggle video
  const toggleVideo = useCallback(() => {
    const videoOff = videoCallService.current.toggleVideo();
    setIsVideoOff(videoOff);
    return videoOff;
  }, []);

  // Start screen sharing
  const startScreenShare = useCallback(async () => {
    try {
      await videoCallService.current.startScreenShare();
      setIsScreenSharing(true);
    } catch (error) {
      console.error('Error starting screen share:', error);
      message.error('Failed to start screen sharing');
    }
  }, []);

  // Stop screen sharing
  const stopScreenShare = useCallback(async () => {
    await videoCallService.current.stopScreenShare();
    setIsScreenSharing(false);
  }, []);

  // Send chat message
  const sendChatMessage = useCallback((messageText) => {
    const message = {
      id: Date.now(),
      message: messageText,
      timestamp: new Date().toISOString(),
      sender: 'local',
    };

    setChatMessages(prev => [...prev, message]);
    videoCallService.current.sendChatMessage(messageText);
  }, []);

  // Open/close chat
  const toggleChat = useCallback(() => {
    setIsChatOpen(prev => {
      if (!prev) {
        setUnreadCount(0);
      }
      return !prev;
    });
  }, []);

  // Format call duration
  const formatDuration = useCallback((seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    // Video refs
    localVideoRef,
    remoteVideoRef,

    // Call states
    isCallActive,
    isConnecting,
    isConnected,
    isMuted,
    isVideoOff,
    isScreenSharing,
    connectionState,
    error,

    // Chat states
    chatMessages,
    unreadCount,
    isChatOpen,

    // Call info
    callDuration,
    formattedDuration: formatDuration(callDuration),
    callQuality,

    // Actions
    initializeCall,
    startCall,
    endCall,
    toggleAudio,
    toggleVideo,
    startScreenShare,
    stopScreenShare,
    sendChatMessage,
    toggleChat,
  };
};

export default useVideoCall;
