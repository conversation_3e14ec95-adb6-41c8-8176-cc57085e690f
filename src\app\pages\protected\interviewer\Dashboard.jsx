import React, { useState, useEffect } from 'react';
import { Row, Col, Typo<PERSON>, <PERSON>, Card, Tooltip, Badge, Progress } from 'antd';
import {
  Fa<PERSON><PERSON>s,
  FaClock,
  FaCheckCircle,
  FaMoneyBillWave,
  FaChartLine,
  FaCalendarCheck,
  FaChartBar,
  FaChartPie,
  FaStar,
  FaFilter,
  FaSort,
  FaUserTie,
} from 'react-icons/fa';
import StatCard from '@/features/interviewer/components/StatCard';
import BarChart from '@/features/interviewer/components/BarChart';
import PieChart from '@/features/interviewer/components/PieChart';
import CandidateTable from '@/features/interviewer/components/CandidateTable';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Text } = Typography;

const RecruiterDashboard = () => {
  const { isMobile, isTablet } = useDeviceDetect();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('This Month');
  const [hiringData, setHiringData] = useState([]);
  const [compositionData, setCompositionData] = useState([]);
  const [candidatesData, setCandidatesData] = useState([]);
  const [performanceData, setPerformanceData] = useState({});
  const [mockInterviewRequests, setMockInterviewRequests] = useState([]);
  const [mockScheduledInterviews, setMockScheduledInterviews] = useState([]);
  const [topPerformers, setTopPerformers] = useState([]);

  useEffect(() => {
    setLoading(true);

    // Simulate API call with setTimeout
    setTimeout(() => {
      // Mock interview requests
      const mockRequests = [
        { id: 1, candidate: 'Rahul Sharma', position: 'Frontend Developer', company: 'TechCorp' },
        { id: 2, candidate: 'Priya Patel', position: 'UX Designer', company: 'DesignHub' },
        { id: 3, candidate: 'Vikram Singh', position: 'DevOps Engineer', company: 'CloudTech' },
        { id: 4, candidate: 'Ananya Gupta', position: 'Product Manager', company: 'InnovateTech' },
        { id: 5, candidate: 'Rajesh Kumar', position: 'Backend Developer', company: 'DataSystems' },
      ];

      // Mock scheduled interviews
      const mockScheduled = [
        {
          id: 1,
          candidate: 'Neha Verma',
          position: 'Data Scientist',
          company: 'AnalyticsPro',
          time: '10:00 AM',
          date: 'Today',
        },
        {
          id: 2,
          candidate: 'Arjun Reddy',
          position: 'Mobile Developer',
          company: 'AppWorks',
          time: '2:30 PM',
          date: 'Tomorrow',
        },
        {
          id: 3,
          candidate: 'Kavita Sharma',
          position: 'HR Manager',
          company: 'PeopleFirst',
          time: '11:00 AM',
          date: '23 Jul 2023',
        },
      ];

      // Hiring statistics data
      const mockHiringData = [
        { month: 'Jan', type: 'Interview Posted', value: 50 },
        { month: 'Jan', type: 'Interview Completed', value: 35 },
        { month: 'Feb', type: 'Interview Posted', value: 60 },
        { month: 'Feb', type: 'Interview Completed', value: 45 },
        { month: 'Mar', type: 'Interview Posted', value: 70 },
        { month: 'Mar', type: 'Interview Completed', value: 55 },
        { month: 'Apr', type: 'Interview Posted', value: 60 },
        { month: 'Apr', type: 'Interview Completed', value: 50 },
        { month: 'May', type: 'Interview Posted', value: 45 },
        { month: 'May', type: 'Interview Completed', value: 35 },
        { month: 'Jun', type: 'Interview Posted', value: 60 },
        { month: 'Jun', type: 'Interview Completed', value: 45 },
        { month: 'Jul', type: 'Interview Posted', value: 65 },
        { month: 'Jul', type: 'Interview Completed', value: 50 },
        { month: 'Aug', type: 'Interview Posted', value: 75 },
        { month: 'Aug', type: 'Interview Completed', value: 60 },
        { month: 'Sep', type: 'Interview Posted', value: 65 },
        { month: 'Sep', type: 'Interview Completed', value: 55 },
        { month: 'Oct', type: 'Interview Posted', value: 60 },
        { month: 'Oct', type: 'Interview Completed', value: 45 },
        { month: 'Nov', type: 'Interview Posted', value: 50 },
        { month: 'Nov', type: 'Interview Completed', value: 40 },
        { month: 'Dec', type: 'Interview Posted', value: 70 },
        { month: 'Dec', type: 'Interview Completed', value: 55 },
      ];

      // Interview composition data
      const mockCompositionData = [
        { type: 'Selected', value: 35 },
        { type: 'Rejected', value: 65 },
      ];

      // Performance metrics
      const mockPerformanceData = {
        interviewCompletionRate: 85,
        candidatePlacementRate: 42,
        averageFeedbackScore: 4.7,
        responseTime: 6, // hours
      };

      // Top performers
      const mockTopPerformers = [
        {
          name: 'Rea Gupta',
          score: 95,
          department: 'Marketing',
          avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        },
        {
          name: 'Rohit Verma',
          score: 80,
          department: 'Finance',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        },
        {
          name: 'Priya Sharma',
          score: 85,
          department: 'HR',
          avatar: 'https://randomuser.me/api/portraits/women/34.jpg',
        },
      ];

      // Candidates data
      const mockCandidatesData = [
        {
          id: 1,
          name: 'Rea Gupta',
          department: 'Marketing',
          age: 22,
          score: 95,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        },
        {
          id: 2,
          name: 'Rohit Verma',
          department: 'Finance',
          age: 24,
          score: 80,
          status: 'Contract',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        },
        {
          id: 3,
          name: 'Sumesh Das',
          department: 'Sales Manager',
          age: 28,
          score: 52,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/men/36.jpg',
        },
        {
          id: 4,
          name: 'Priya Sharma',
          department: 'HR',
          age: 26,
          score: 85,
          status: 'Contract',
          avatar: 'https://randomuser.me/api/portraits/women/34.jpg',
        },
        {
          id: 5,
          name: 'Amit Kumar',
          department: 'IT',
          age: 30,
          score: 78,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/men/40.jpg',
        },
        {
          id: 6,
          name: 'Neha Patel',
          department: 'Marketing',
          age: 23,
          score: 90,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/women/48.jpg',
        },
        {
          id: 7,
          name: 'Rajesh Jain',
          department: 'Finance',
          age: 29,
          score: 88,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/men/38.jpg',
        },
        {
          id: 8,
          name: 'Sneha Verma',
          department: 'Sales',
          age: 25,
          score: 82,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/women/42.jpg',
        },
        {
          id: 9,
          name: 'Vikram Singh',
          department: 'IT',
          age: 27,
          score: 84,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/men/30.jpg',
        },
        {
          id: 10,
          name: 'Kavita Sharma',
          department: 'HR',
          age: 24,
          score: 89,
          status: 'Permanent',
          avatar: 'https://randomuser.me/api/portraits/women/46.jpg',
        },
      ];

      setHiringData(mockHiringData);
      setCompositionData(mockCompositionData);
      setCandidatesData(mockCandidatesData);
      setMockInterviewRequests(mockRequests);
      setMockScheduledInterviews(mockScheduled);
      setPerformanceData(mockPerformanceData);
      setTopPerformers(mockTopPerformers);
      setLoading(false);
    }, 1000);
  }, []);

  // Helper function to get color based on value
  const getColorByValue = (value, threshold1 = 33, threshold2 = 66) => {
    if (value < threshold1) return '#f5222d'; // Red
    if (value < threshold2) return '#faad14'; // Yellow
    return '#52c41a'; // Green
  };

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}
      >
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="recruiter-dashboard">
      <div className="dashboard-header mb-6">
        <Row
          gutter={[16, 16]}
          align="middle"
        >
          <Col
            xs={24}
            md={12}
          >
            <Title
              level={2}
              className="mb-0"
            >
              Recruiter Dashboard
            </Title>
            <Text type="secondary">
              Welcome back! Here's what's happening with your recruitment activities.
            </Text>
          </Col>
          <Col
            xs={24}
            md={12}
            className="text-right"
          >
            <Badge
              count={mockInterviewRequests.length}
              className="mr-4"
            >
              <Tooltip title="Pending Interview Requests">
                <Card className="inline-block p-2 cursor-pointer hover:shadow-md">
                  <FaClock
                    size={20}
                    className="text-blue-500"
                  />
                </Card>
              </Tooltip>
            </Badge>
            <Badge
              count={mockScheduledInterviews.length}
              className="mr-4"
            >
              <Tooltip title="Upcoming Interviews">
                <Card className="inline-block p-2 cursor-pointer hover:shadow-md">
                  <FaCalendarCheck
                    size={20}
                    className="text-green-500"
                  />
                </Card>
              </Tooltip>
            </Badge>
            <Tooltip title="Your Performance">
              <Card className="inline-block p-2 cursor-pointer hover:shadow-md">
                <FaChartLine
                  size={20}
                  className="text-purple-500"
                />
              </Card>
            </Tooltip>
          </Col>
        </Row>
      </div>

      {/* Key Metrics */}
      <Row
        gutter={[16, 16]}
        className="mb-6"
      >
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <StatCard
            icon={<FaUsers size={24} />}
            title="Total Candidates"
            value={candidatesData.length}
            subtitle={`+${candidatesData.length - 8} from last month`}
            color="#1890ff"
          />
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <StatCard
            icon={<FaUserTie size={24} />}
            title="Interview Requests"
            value={mockInterviewRequests.length}
            subtitle="Pending Interviews"
            color="#f5222d"
          />
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <StatCard
            icon={<FaClock size={24} />}
            title="Scheduled Interviews"
            value={mockScheduledInterviews.length}
            subtitle="Next 7 days"
            color="#2fc25b"
          />
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <StatCard
            icon={<FaMoneyBillWave size={24} />}
            title="Total Interviews"
            value={mockInterviewRequests.length + mockScheduledInterviews.length}
            subtitle="This month"
            color="#faad14"
          />
        </Col>
      </Row>

      {/* Performance Metrics */}
      <Row
        gutter={[16, 16]}
        className="mb-6"
      >
        <Col
          xs={24}
          className="p-4 rounded-lg"
        >
          <Card
            title={
              <div className="flex items-center">
                <FaChartLine
                  className="mr-2 text-purple-500"
                  size={18}
                />
                <span>Your Performance Metrics</span>
              </div>
            }
          >
            <Row gutter={[24, 24]}>
              <Col
                xs={24}
                sm={12}
                md={6}
              >
                <div className="text-center">
                  <Progress
                    type="dashboard"
                    percent={performanceData.interviewCompletionRate}
                    strokeColor={getColorByValue(performanceData.interviewCompletionRate)}
                  />
                  <div className="mt-2">
                    <Text strong>Interview Completion Rate</Text>
                  </div>
                </div>
              </Col>
              <Col
                xs={24}
                sm={12}
                md={6}
              >
                <div className="text-center">
                  <Progress
                    type="dashboard"
                    percent={performanceData.candidatePlacementRate}
                    strokeColor={getColorByValue(performanceData.candidatePlacementRate, 20, 40)}
                  />
                  <div className="mt-2">
                    <Text strong>Candidate Placement Rate</Text>
                  </div>
                </div>
              </Col>
              <Col
                xs={24}
                sm={12}
                md={6}
              >
                <div className="text-center">
                  <div className="rating-display">
                    <Text style={{ fontSize: '36px', fontWeight: 'bold' }}>
                      {performanceData.averageFeedbackScore}
                    </Text>
                    <div className="flex justify-center mt-2">
                      {[...Array(5)].map((_, i) => (
                        <FaStar
                          key={i}
                          size={16}
                          className={
                            i < Math.floor(performanceData.averageFeedbackScore)
                              ? 'text-yellow-400'
                              : 'text-gray-300'
                          }
                        />
                      ))}
                    </div>
                  </div>
                  <div className="mt-2">
                    <Text strong>Average Feedback Score</Text>
                  </div>
                </div>
              </Col>
              <Col
                xs={24}
                sm={12}
                md={6}
              >
                <div className="text-center">
                  <div className="response-time">
                    <Text style={{ fontSize: '36px', fontWeight: 'bold' }}>
                      {performanceData.responseTime}
                    </Text>
                    <Text style={{ fontSize: '16px' }}>hrs</Text>
                  </div>
                  <div className="mt-2">
                    <Text strong>Average Response Time</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row
        gutter={[16, 16]}
        className="mb-6"
      >
        <Col
          xs={24}
          md={16}
        >
          <Card
            title={
              <div className="flex items-center">
                <FaChartBar
                  className="mr-2 text-blue-500"
                  size={18}
                />
                <span>Hiring Statistics</span>
              </div>
            }
          >
            <BarChart
              data={hiringData}
              timeRange={timeRange}
              setTimeRange={setTimeRange}
              title=""
            />
          </Card>
        </Col>
        <Col
          xs={24}
          md={8}
        >
          <Card
            title={
              <div className="flex items-center">
                <FaChartPie
                  className="mr-2 text-green-500"
                  size={18}
                />
                <span>Interview Composition</span>
              </div>
            }
          >
            <PieChart
              data={compositionData}
              title=""
            />
          </Card>
        </Col>
      </Row>

      {/* Top Performers */}
      <Row
        gutter={[16, 16]}
        className="mb-6"
      >
        <Col xs={24}>
          <Card
            title={
              <div className="flex items-center">
                <FaStar
                  className="mr-2 text-yellow-500"
                  size={18}
                />
                <span>Top Performers</span>
              </div>
            }
          >
            <Row gutter={[16, 16]}>
              {topPerformers.map((performer, index) => (
                <Col
                  xs={24}
                  sm={8}
                  key={index}
                >
                  <Card
                    className="text-center"
                    bordered={false}
                  >
                    <div className="flex flex-col items-center">
                      <div className="relative mb-2">
                        <img
                          src={performer.avatar}
                          alt={performer.name}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                        <div className="absolute -top-2 -right-2 bg-yellow-400 rounded-full w-8 h-8 flex items-center justify-center text-white font-bold">
                          {index + 1}
                        </div>
                      </div>
                      <Text strong>{performer.name}</Text>
                      <Text type="secondary">{performer.department}</Text>
                      <div className="mt-2 flex">
                        {[...Array(5)].map((_, i) => (
                          <FaStar
                            key={i}
                            size={14}
                            className={
                              i < performer.score / 10 ? 'text-yellow-400' : 'text-gray-300'
                            }
                          />
                        ))}
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Candidate Table */}
      <Row gutter={[16, 16]}>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={24}
        >
          <CandidateTable
            data={candidatesData}
            title="Top Candidates"
          />
        </Col>
      </Row>
    </div>
  );
};

export default RecruiterDashboard;
