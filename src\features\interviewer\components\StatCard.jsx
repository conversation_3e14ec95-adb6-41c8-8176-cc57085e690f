import React from 'react';
import { Card, Typography } from 'antd';
import { useColorModeStore } from '@/store/colorMode.store';

const { Title, Text } = Typography;

const StatCard = ({ title, value, subtitle, icon, color = '#1890ff' }) => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  return (
    <Card
      className="stat-card h-full transition-all shadow-sm hover:shadow-lg"
      style={{
        borderRadius: '12px',
        borderColor: 'transparent',
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.04)' : 'white',
      }}
    >
      <div className="flex items-start justify-between">
        <div>
          <Text
            className="text-muted mb-1 block"
            style={{ fontSize: '14px' }}
          >
            {title}
          </Text>
          <Title
            level={2}
            className="m-0 font-bold"
            style={{ marginBottom: '4px' }}
          >
            {value}
          </Title>
          <Text
            className="text-muted"
            style={{ fontSize: '13px' }}
          >
            {subtitle}
          </Text>
        </div>
        {icon && (
          <div
            className="flex items-center justify-center rounded-full p-2"
            style={{
              backgroundColor: `${color}15`,
              color: color,
              width: '40px',
              height: '40px',
            }}
          >
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
};

export default StatCard;
