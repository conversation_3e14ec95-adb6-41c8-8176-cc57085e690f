import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Briefcase,
  FileText,
  Video,
  User,
  Settings,
  LogOut,
  MessageSquare,
} from 'lucide-react';
import ColorModeToggle from '@/components/shared/ColorModeToggle';
import useLogout from '@/hooks/useLogout';

/**
 * Mobile drawer component for the Candidate Layout
 *
 * @param {Object} props
 * @param {boolean} props.open - Whether the drawer is open
 * @param {Function} props.onClose - Function to close the drawer
 * @param {Function} props.handleLogout - Function to handle logout
 * @param {string} props.currentPage - Current page key
 * @param {boolean} props.isDark - Dark mode flag
 * @param {string} props.logo_dark - Dark mode logo path
 * @param {string} props.logo_lite - Light mode logo path
 */
const CandidateMobileDrawer = ({ open, onClose, currentPage, isDark, logo_dark, logo_lite }) => {
  const navigate = useNavigate();
  const { handleLogout, isLoggingOut } = useLogout();
  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="Flyt"
            className="h-8"
          />
          <ColorModeToggle
            type="switch"
            size="small"
          />
        </div>
      }
      placement="right"
      onClose={onClose}
      open={open}
      width={320}
      styles={{
        body: { padding: '16px' },
        header: { padding: '16px 24px' },
      }}
    >
      <Menu
        mode="vertical"
        selectedKeys={[currentPage]}
        style={{ border: 'none' }}
        items={[
          {
            key: 'dashboard',
            icon: <LayoutDashboard size={18} />,
            label: 'Home',
            onClick: () => {
              navigate('/candidate/dashboard');
              onClose();
            },
          },
          {
            key: 'jobs',
            icon: <Briefcase size={18} />,
            label: 'Jobs',
            onClick: () => {
              navigate('/candidate/jobs');
              onClose();
            },
          },
          {
            key: 'applications',
            icon: <FileText size={18} />,
            label: 'Applications',
            onClick: () => {
              navigate('/candidate/applications');
              onClose();
            },
          },
          {
            key: 'interviews',
            icon: <Video size={18} />,
            label: 'Interviews',
            onClick: () => {
              navigate('/candidate/interviews');
              onClose();
            },
          },
          {
            key: 'messages',
            icon: <MessageSquare size={18} />,
            label: 'Messages',
            onClick: () => {
              navigate('/candidate/messages');
              onClose();
            },
          },
          {
            key: 'profile',
            icon: <User size={18} />,
            label: 'My Profile',
            onClick: () => {
              navigate('/candidate/profile');
              onClose();
            },
          },
          {
            key: 'settings',
            icon: <Settings size={18} />,
            label: 'Settings',
            onClick: () => {
              navigate('/candidate/settings');
              onClose();
            },
          },
        ]}
      />

      <Divider />

      <Button
        type="primary"
        danger
        icon={<LogOut size={16} />}
        onClick={handleLogout}
        block
        className="mt-2"
        loading={isLoggingOut}
      >
        {isLoggingOut ? 'Logging out...' : 'Logout'}
      </Button>
    </Drawer>
  );
};

export default CandidateMobileDrawer;
