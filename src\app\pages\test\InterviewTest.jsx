/**
 * Interview Test Page
 *
 * A test page to help test the interview video call functionality
 * Provides buttons to create test interview links and navigate to them
 */

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Space,
  Typography,
  Alert,
  Input,
  Form,
  Select,
  message,
  Divider,
  List,
  Tag,
} from 'antd';
import {
  VideoCameraOutlined,
  PlusOutlined,
  LinkOutlined,
  UserOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { generateInterviewLink } from '@/services/interviewLink.service';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const InterviewTest = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLinks, setTestLinks] = useState([]);

  // Sample test data with proper UUIDs
  const sampleInterviewData = {
    candidate_id: user?.id || '00000000-0000-0000-0000-000000000001',
    interviewer_id: '00000000-0000-0000-0000-000000000002',
    company_id: '00000000-0000-0000-0000-000000000003',
    job_id: '00000000-0000-0000-0000-000000000004',
    interview_date: dayjs().add(1, 'hour').toISOString(),
    duration_minutes: 60,
  };

  const handleCreateTestLink = async (values) => {
    try {
      setLoading(true);

      const interviewData = {
        ...sampleInterviewData,
        ...values,
        interview_date: values.interview_date
          ? dayjs(values.interview_date).toISOString()
          : sampleInterviewData.interview_date,
      };

      const result = await generateInterviewLink(interviewData);

      if (result.success) {
        const newLink = {
          id: result.data.id,
          url: result.data.interview_url,
          linkId: result.data.link_id,
          createdAt: new Date().toISOString(),
          duration: values.duration_minutes || 60,
          type: values.type || 'test',
        };

        setTestLinks((prev) => [newLink, ...prev]);
        message.success('Test interview link created successfully!');
        form.resetFields();
      } else {
        message.error(result.error || 'Failed to create test link');
      }
    } catch (error) {
      console.error('Error creating test link:', error);
      message.error('Failed to create test link');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinInterview = (linkId) => {
    // Navigate to the interview room
    navigate(`/interview-room/${linkId}`);
  };

  const handleJoinAsRole = (linkId, role) => {
    // Navigate to role-specific interview room
    const basePath = role === 'candidate' ? '/candidate' : role === 'company' ? '/org' : '/sourcer';
    navigate(`${basePath}/interview-room/${linkId}`);
  };

  return (
    <div className="interview-test-page p-6 max-w-4xl mx-auto">
      <Card>
        <Title level={2}>
          <VideoCameraOutlined className="mr-2" />
          Interview Video Call Test
        </Title>

        <Alert
          message="Test Interview Video Call System"
          description="Use this page to create test interview links and test the video call functionality. You can create links and join them as different roles."
          type="info"
          showIcon
          className="mb-6"
        />

        {/* Create Test Link Form */}
        <Card
          title="Create Test Interview Link"
          className="mb-6"
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateTestLink}
            initialValues={{
              duration_minutes: 60,
              type: 'test',
            }}
          >
            <Form.Item
              name="type"
              label="Test Type"
              rules={[{ required: true, message: 'Please select test type' }]}
            >
              <Select placeholder="Select test type">
                <Option value="test">General Test</Option>
                <Option value="candidate-test">Candidate Test</Option>
                <Option value="interviewer-test">Interviewer Test</Option>
                <Option value="company-test">Company Test</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="duration_minutes"
              label="Duration (minutes)"
              rules={[{ required: true, message: 'Please enter duration' }]}
            >
              <Select placeholder="Select duration">
                <Option value={30}>30 minutes</Option>
                <Option value={45}>45 minutes</Option>
                <Option value={60}>1 hour</Option>
                <Option value={90}>1.5 hours</Option>
                <Option value={120}>2 hours</Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<PlusOutlined />}
              >
                Create Test Link
              </Button>
            </Form.Item>
          </Form>
        </Card>

        {/* Test Links List */}
        {testLinks.length > 0 && (
          <Card title="Generated Test Links">
            <List
              dataSource={testLinks}
              renderItem={(link) => (
                <List.Item
                  actions={[
                    <Button
                      key="global"
                      type="primary"
                      icon={<LinkOutlined />}
                      onClick={() => handleJoinInterview(link.linkId)}
                    >
                      Join (Global)
                    </Button>,
                    <Button
                      key="candidate"
                      icon={<UserOutlined />}
                      onClick={() => handleJoinAsRole(link.linkId, 'candidate')}
                    >
                      Join as Candidate
                    </Button>,
                    <Button
                      key="company"
                      icon={<TeamOutlined />}
                      onClick={() => handleJoinAsRole(link.linkId, 'company')}
                    >
                      Join as Company
                    </Button>,
                    <Button
                      key="interviewer"
                      icon={<TeamOutlined />}
                      onClick={() => handleJoinAsRole(link.linkId, 'interviewer')}
                    >
                      Join as Interviewer
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>Test Link #{link.linkId}</Text>
                        <Tag color="blue">{link.type}</Tag>
                        <Tag color="green">{link.duration} min</Tag>
                      </Space>
                    }
                    description={
                      <Space
                        direction="vertical"
                        size={0}
                      >
                        <Text type="secondary">
                          Created: {dayjs(link.createdAt).format('MMM DD, YYYY HH:mm')}
                        </Text>
                        <Text
                          code
                          copyable
                        >
                          {link.url}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        )}

        <Divider />

        {/* Instructions */}
        <Card title="How to Test">
          <Space
            direction="vertical"
            size="middle"
            className="w-full"
          >
            <div>
              <Title level={4}>1. Create Test Links</Title>
              <Paragraph>
                Use the form above to create test interview links. Each link will have a unique ID
                that you can use to join the interview room.
              </Paragraph>
            </div>

            <div>
              <Title level={4}>2. Test Different Routes</Title>
              <Paragraph>You can test the interview room through different routes:</Paragraph>
              <ul>
                <li>
                  <Text code>/interview-room/:linkId</Text> - Global route (accessible by all)
                </li>
                <li>
                  <Text code>/candidate/interview-room/:linkId</Text> - Candidate-specific route
                </li>
                <li>
                  <Text code>/org/interview-room/:linkId</Text> - Company-specific route
                </li>
                <li>
                  <Text code>/sourcer/interview-room/:linkId</Text> - Interviewer-specific route
                </li>
              </ul>
            </div>

            <div>
              <Title level={4}>3. Test Video Call Features</Title>
              <Paragraph>Once in the interview room, you can test:</Paragraph>
              <ul>
                <li>Camera and microphone access</li>
                <li>Video/audio toggle controls</li>
                <li>Interview timer and duration</li>
                <li>Participant information display</li>
                <li>End interview functionality</li>
              </ul>
            </div>

            <div>
              <Title level={4}>4. Browser Permissions</Title>
              <Alert
                message="Camera and Microphone Access"
                description="Make sure to allow camera and microphone access when prompted by your browser. This is required for the video call functionality to work properly."
                type="warning"
                showIcon
              />
            </div>
          </Space>
        </Card>
      </Card>
    </div>
  );
};

export default InterviewTest;
