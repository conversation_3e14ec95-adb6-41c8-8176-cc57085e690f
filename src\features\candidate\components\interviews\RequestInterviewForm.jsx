/**
 * RequestInterviewForm Component
 *
 * Candidate-specific inline component for creating interview requests
 * Replaces the tabs view with a beautiful calendar-integrated interface
 * Uses CANDIDATE_ROLES constant for position options
 */

import { useState, useEffect } from 'react';
import {
  Form,
  Input,
  DatePicker,
  Select,
  Button,
  Space,
  Typography,
  Alert,
  message,
  TimePicker,
  Card,
  Row,
  Col,
  Calendar,
  Badge,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  UserOutlined,
  VideoCameraOutlined,
  PhoneOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  BuildOutlined,
} from '@ant-design/icons';
import { createInterviewRequest } from '@/services/interview.service';
import useAuth from '@/hooks/useAuth';
import { useJobs } from '@/features/candidate/hooks';
import { CANDIDATE_ROLES, DEMO_COMPANIES } from '@/features/candidate/constants';
import dayjs from 'dayjs';

const { Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const RequestInterviewForm = ({ onSuccess, onCancel }) => {
  const [form] = Form.useForm();
  const { user, profile } = useAuth();
  const { jobs } = useJobs();
  const [loading, setLoading] = useState(false);
  const [availableJobs, setAvailableJobs] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedPosition, setSelectedPosition] = useState(null);

  // Load available jobs for interview requests
  useEffect(() => {
    if (jobs && jobs.length > 0) {
      // Filter jobs that candidate has applied to or can apply to
      const eligibleJobs = jobs.filter(
        (job) => job.status === 'active' && (!job.isApplied || job.isApplied)
      );
      setAvailableJobs(eligibleJobs);
    } else {
      // Use CANDIDATE_ROLES constant when no jobs available
      const constantJobs = CANDIDATE_ROLES.map((role, index) => ({
        id: `const_${index}`,
        title: role,
        companies: {
          company_name:
            DEMO_COMPANIES[index % DEMO_COMPANIES.length]?.company_name || 'Demo Company',
          company_logo_url: DEMO_COMPANIES[index % DEMO_COMPANIES.length]?.logo_url,
        },
        company_id: `demo_company_${index % DEMO_COMPANIES.length}`,
        status: 'active',
      }));
      setAvailableJobs(constantJobs);
    }
  }, [jobs]);

  // Handle form submission
  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      // Combine date and time
      const interviewDateTime = selectedDate
        .hour(values.preferredTime.hour())
        .minute(values.preferredTime.minute());

      const requestData = {
        candidate_id: user.id,
        job_id: values.jobId,
        company_id: values.companyId,
        preferred_date: interviewDateTime.toISOString(),
        duration_minutes: values.duration || 60,
        interview_type: values.interviewType || 'video',
        message: values.message,
        status: 'requested',
      };

      // Get company ID from selected job
      const selectedJob = availableJobs.find((job) => job.id === values.jobId);
      if (selectedJob) {
        requestData.company_id = selectedJob.company_id;
      }

      const result = await createInterviewRequest(requestData);

      if (result.success) {
        message.success('Interview request created successfully! Interviewers will be notified.');
        form.resetFields();
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        message.error(result.error || 'Failed to create interview request');
      }
    } catch (error) {
      console.error('Error creating interview request:', error);
      message.error('Failed to create interview request');
    } finally {
      setLoading(false);
    }
  };

  // Handle position selection change
  const handleSelectPosition = (position) => {
    console.log('Candidate role handle: ', position);

    // const selectedJob = CANDIDATE_ROLES.find((role, index) => index === jobId);
    // console.log('selectedJob: ', selectedJob);
    if (position) {
      setSelectedPosition(position);
      form.setFieldsValue({
        companyId: position,
      });
    }
  };

  // Handle calendar date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    form.setFieldsValue({
      preferredDate: date,
    });
  };

  // Calendar cell renderer
  const dateCellRender = (value) => {
    const isSelected = selectedDate && value.isSame(selectedDate, 'day');
    const isToday = value.isSame(dayjs(), 'day');
    const isPast = value.isBefore(dayjs(), 'day');

    if (isPast) return null;

    return (
      <div className={`calendar-cell ${isSelected ? 'selected' : ''} ${isToday ? 'today' : ''}`}>
        {isSelected && <Badge status="processing" />}
      </div>
    );
  };

  // Get interview type icon
  const getInterviewTypeIcon = (type) => {
    switch (type) {
      case 'video':
        return <VideoCameraOutlined />;
      case 'phone':
        return <PhoneOutlined />;
      case 'in-person':
        return <TeamOutlined />;
      default:
        return <VideoCameraOutlined />;
    }
  };

  return (
    <div className="request-interview-form">
      <Alert
        message="Request an Interview"
        description="Fill in your preferences below and submit your interview request. Available interviewers will be notified and can accept your request."
        type="info"
        showIcon
        className="mb-6"
      />

      <Row gutter={24}>
        {/* Left Column - Form */}
        <Col
          xs={24}
          lg={14}
        >
          <Card
            title="Interview Details"
            className="mb-6"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                duration: 60,
                interviewType: 'video',
                preferredTime: dayjs().hour(10).minute(0),
              }}
            >
              {/* Position Selection */}
              <Form.Item
                name="jobId"
                label={
                  <Space>
                    <BuildOutlined />
                    Select Position
                  </Space>
                }
                rules={[{ required: true, message: 'Please select a position' }]}
              >
                <Select
                  placeholder="Choose the position you want to interview for"
                  onChange={handleSelectPosition}
                  showSearch
                  size="large"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {CANDIDATE_ROLES.map((role, id) => (
                    <Option
                      key={id + role}
                      value={role}
                    >
                      <Space>
                        <BuildOutlined />
                        <span>{role}</span>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {/* Date and Time */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="preferredDate"
                    label={
                      <Space>
                        <CalendarOutlined />
                        Preferred Date
                      </Space>
                    }
                    rules={[{ required: true, message: 'Please select preferred date' }]}
                  >
                    <DatePicker
                      className="w-full"
                      size="large"
                      disabledDate={(current) => current && current < dayjs().startOf('day')}
                      placeholder="Select date"
                      onChange={handleDateSelect}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="preferredTime"
                    label={
                      <Space>
                        <ClockCircleOutlined />
                        Preferred Time
                      </Space>
                    }
                    rules={[{ required: true, message: 'Please select preferred time' }]}
                  >
                    <TimePicker
                      className="w-full"
                      size="large"
                      format="HH:mm"
                      minuteStep={30}
                      placeholder="Select time"
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Duration and Type */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="duration"
                    label="Expected Duration"
                    rules={[{ required: true, message: 'Please select duration' }]}
                  >
                    <Select
                      placeholder="Select duration"
                      size="large"
                    >
                      <Option value={30}>30 minutes</Option>
                      <Option value={45}>45 minutes</Option>
                      <Option value={60}>1 hour</Option>
                      <Option value={90}>1.5 hours</Option>
                      <Option value={120}>2 hours</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="interviewType"
                    label="Interview Type"
                    rules={[{ required: true, message: 'Please select interview type' }]}
                  >
                    <Select
                      placeholder="Select interview type"
                      size="large"
                    >
                      <Option value="video">
                        <Space>
                          <VideoCameraOutlined />
                          Video Call
                        </Space>
                      </Option>
                      <Option value="phone">
                        <Space>
                          <PhoneOutlined />
                          Phone Call
                        </Space>
                      </Option>
                      <Option value="in-person">
                        <Space>
                          <TeamOutlined />
                          In-Person
                        </Space>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {/* Message */}
              <Form.Item
                name="message"
                label={
                  <Space>
                    <MessageOutlined />
                    Message to Interviewer (Optional)
                  </Space>
                }
              >
                <TextArea
                  rows={4}
                  placeholder="Add any specific requirements, topics you'd like to discuss, or questions you have..."
                  maxLength={500}
                  showCount
                />
              </Form.Item>

              {/* Submit Button */}
              <Form.Item className="mb-0">
                <Space className="w-full justify-end">
                  <Button
                    onClick={onCancel}
                    size="large"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<CheckCircleOutlined />}
                    size="large"
                  >
                    Submit Request
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Right Column - Calendar and Preview */}
        <Col
          xs={24}
          lg={10}
        >
          {/* Calendar */}
          <Card
            title="Select Interview Date"
            className="mb-6"
          >
            <Calendar
              fullscreen={false}
              onSelect={handleDateSelect}
              dateCellRender={dateCellRender}
              disabledDate={(current) => current && current < dayjs().startOf('day')}
            />
          </Card>

          {/* Request Preview */}
          {selectedPosition && (
            <Card
              title="Request Preview"
              className="mb-6"
            >
              <Space
                direction="vertical"
                className="w-full"
              >
                <div>
                  <Text strong>Position:</Text>
                  <br />
                  <Text>{selectedPosition.title}</Text>
                </div>
                <div>
                  <Text strong>Company:</Text>
                  <br />
                  <Text>{selectedPosition.companies?.company_name}</Text>
                </div>
                {selectedDate && (
                  <div>
                    <Text strong>Preferred Date:</Text>
                    <br />
                    <Text>{selectedDate.format('MMMM DD, YYYY')}</Text>
                  </div>
                )}
              </Space>
            </Card>
          )}

          {/* What Happens Next */}
          <Card
            title="What happens next?"
            className="bg-blue-50 dark:bg-blue-900/20"
          >
            <Space
              direction="vertical"
              size={12}
            >
              <div className="flex items-start space-x-3">
                <CheckCircleOutlined className="text-green-500 mt-1" />
                <Text>Your request will be visible to all available interviewers</Text>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircleOutlined className="text-green-500 mt-1" />
                <Text>Once an interviewer accepts, you'll receive a notification</Text>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircleOutlined className="text-green-500 mt-1" />
                <Text>The interview will appear in your "Upcoming" tab</Text>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircleOutlined className="text-green-500 mt-1" />
                <Text>You'll receive a calendar invitation with meeting details</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      <style
        jsx
        global
      >{`
        .request-interview-form .ant-picker-calendar-date-content {
          height: 40px;
        }

        .request-interview-form
          .ant-picker-calendar
          .ant-picker-cell-selected
          .ant-picker-calendar-date {
          background: #1890ff;
          color: white;
        }

        .request-interview-form
          .ant-picker-calendar
          .ant-picker-cell-selected
          .ant-picker-calendar-date:hover {
          background: #40a9ff;
        }

        .request-interview-form
          .ant-picker-calendar
          .ant-picker-cell:hover
          .ant-picker-calendar-date {
          background: #e6f7ff;
        }

        .calendar-cell {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .calendar-cell.selected {
          background-color: #e6f7ff;
          border-radius: 4px;
        }

        .calendar-cell.today {
          border: 1px solid #1890ff;
          border-radius: 4px;
        }
      `}</style>
    </div>
  );
};

export default RequestInterviewForm;
