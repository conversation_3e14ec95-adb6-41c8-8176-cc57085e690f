-- Update interviews table to support the new interview request workflow
-- This migration adds necessary columns and constraints for the request system

-- Add new columns to interviews table
ALTER TABLE interviews 
ADD COLUMN IF NOT EXISTS preferred_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS interview_type TEXT DEFAULT 'video' CHECK (interview_type IN ('video', 'phone', 'in-person')),
ADD COLUMN IF NOT EXISTS message TEXT,
ADD COLUMN IF NOT EXISTS accepted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS declined_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS decline_reason TEXT;

-- Update the status check constraint to include new statuses
ALTER TABLE interviews DROP CONSTRAINT IF EXISTS interviews_status_check;
ALTER TABLE interviews ADD CONSTRAINT interviews_status_check 
CHECK (status IN ('requested', 'scheduled', 'completed', 'cancelled', 'declined'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_interviews_status ON interviews(status);
CREATE INDEX IF NOT EXISTS idx_interviews_preferred_date ON interviews(preferred_date);
CREATE INDEX IF NOT EXISTS idx_interviews_candidate_status ON interviews(candidate_id, status);
CREATE INDEX IF NOT EXISTS idx_interviews_interviewer_status ON interviews(interviewer_id, status);
CREATE INDEX IF NOT EXISTS idx_interviews_company_status ON interviews(company_id, status);

-- Update RLS policies for the new workflow

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their interviews" ON interviews;
DROP POLICY IF EXISTS "Users can create interviews" ON interviews;
DROP POLICY IF EXISTS "Users can update their interviews" ON interviews;

-- Policy: Candidates can view their own interviews
CREATE POLICY "Candidates can view their interviews" ON interviews
  FOR SELECT USING (
    candidate_id = auth.uid()
  );

-- Policy: Interviewers can view interviews assigned to them or pending requests
CREATE POLICY "Interviewers can view interviews" ON interviews
  FOR SELECT USING (
    interviewer_id = auth.uid() OR 
    (status = 'requested' AND interviewer_id IS NULL)
  );

-- Policy: Companies can view interviews for their jobs
CREATE POLICY "Companies can view their interviews" ON interviews
  FOR SELECT USING (
    company_id = auth.uid()
  );

-- Policy: Candidates can create interview requests
CREATE POLICY "Candidates can create interview requests" ON interviews
  FOR INSERT WITH CHECK (
    candidate_id = auth.uid() AND
    status = 'requested'
  );

-- Policy: Candidates can update their own interview requests (before acceptance)
CREATE POLICY "Candidates can update their requests" ON interviews
  FOR UPDATE USING (
    candidate_id = auth.uid() AND
    status = 'requested'
  );

-- Policy: Interviewers can accept interview requests
CREATE POLICY "Interviewers can accept requests" ON interviews
  FOR UPDATE USING (
    status = 'requested' AND
    interviewer_id IS NULL
  ) WITH CHECK (
    interviewer_id = auth.uid() AND
    status IN ('scheduled', 'declined')
  );

-- Policy: Interviewers can update their assigned interviews
CREATE POLICY "Interviewers can update their interviews" ON interviews
  FOR UPDATE USING (
    interviewer_id = auth.uid()
  );

-- Policy: Companies can update interviews for their jobs
CREATE POLICY "Companies can update their interviews" ON interviews
  FOR UPDATE USING (
    company_id = auth.uid()
  );

-- Create function to notify when interview status changes
CREATE OR REPLACE FUNCTION notify_interview_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Notify when interview is accepted
  IF OLD.status = 'requested' AND NEW.status = 'scheduled' THEN
    -- Here you could add notification logic
    -- For now, we'll just update the accepted_at timestamp
    NEW.accepted_at = NOW();
  END IF;
  
  -- Notify when interview is declined
  IF OLD.status = 'requested' AND NEW.status = 'declined' THEN
    NEW.declined_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for interview status changes
DROP TRIGGER IF EXISTS trigger_interview_status_change ON interviews;
CREATE TRIGGER trigger_interview_status_change
  BEFORE UPDATE ON interviews
  FOR EACH ROW
  EXECUTE FUNCTION notify_interview_status_change();

-- Create function to prevent multiple interviewers accepting same request
CREATE OR REPLACE FUNCTION prevent_double_acceptance()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if interview is still in requested status
  IF NEW.status = 'scheduled' AND NEW.interviewer_id IS NOT NULL THEN
    -- Verify the interview is still available
    IF EXISTS (
      SELECT 1 FROM interviews 
      WHERE id = NEW.id 
      AND status = 'requested' 
      AND interviewer_id IS NULL
    ) THEN
      RETURN NEW;
    ELSE
      RAISE EXCEPTION 'Interview request has already been accepted by another interviewer';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent double acceptance
DROP TRIGGER IF EXISTS trigger_prevent_double_acceptance ON interviews;
CREATE TRIGGER trigger_prevent_double_acceptance
  BEFORE UPDATE ON interviews
  FOR EACH ROW
  WHEN (NEW.status = 'scheduled' AND OLD.status = 'requested')
  EXECUTE FUNCTION prevent_double_acceptance();

-- Create view for interview requests (for interviewers)
CREATE OR REPLACE VIEW interview_requests AS
SELECT 
  i.*,
  cp.full_name as candidate_name,
  cp.email as candidate_email,
  cp.profile_photo_url as candidate_photo,
  cp.current_job_title,
  cp.years_experience,
  j.title as job_title,
  j.experience_level,
  j.required_skills,
  comp.company_name,
  comp.company_logo_url
FROM interviews i
LEFT JOIN candidate_profiles cp ON i.candidate_id = cp.id
LEFT JOIN jobs j ON i.job_id = j.id
LEFT JOIN company_profiles comp ON i.company_id = comp.id
WHERE i.status = 'requested' 
AND i.interviewer_id IS NULL;

-- Grant permissions on the view
GRANT SELECT ON interview_requests TO authenticated;

-- Create view for candidate's interview history
CREATE OR REPLACE VIEW candidate_interview_history AS
SELECT 
  i.*,
  ip.full_name as interviewer_name,
  ip.current_designation as interviewer_title,
  ip.profile_photo_url as interviewer_photo,
  j.title as job_title,
  comp.company_name,
  comp.company_logo_url
FROM interviews i
LEFT JOIN interviewer_profiles ip ON i.interviewer_id = ip.id
LEFT JOIN jobs j ON i.job_id = j.id
LEFT JOIN company_profiles comp ON i.company_id = comp.id
WHERE i.candidate_id = auth.uid();

-- Grant permissions on the view
GRANT SELECT ON candidate_interview_history TO authenticated;

-- Create view for interviewer's interview schedule
CREATE OR REPLACE VIEW interviewer_interview_schedule AS
SELECT 
  i.*,
  cp.full_name as candidate_name,
  cp.email as candidate_email,
  cp.profile_photo_url as candidate_photo,
  cp.current_job_title,
  j.title as job_title,
  comp.company_name,
  comp.company_logo_url
FROM interviews i
LEFT JOIN candidate_profiles cp ON i.candidate_id = cp.id
LEFT JOIN jobs j ON i.job_id = j.id
LEFT JOIN company_profiles comp ON i.company_id = comp.id
WHERE i.interviewer_id = auth.uid();

-- Grant permissions on the view
GRANT SELECT ON interviewer_interview_schedule TO authenticated;

-- Add comments for documentation
COMMENT ON COLUMN interviews.preferred_date IS 'Candidate preferred date/time for the interview';
COMMENT ON COLUMN interviews.interview_type IS 'Type of interview: video, phone, or in-person';
COMMENT ON COLUMN interviews.message IS 'Message from candidate to interviewer';
COMMENT ON COLUMN interviews.accepted_at IS 'Timestamp when interview was accepted by interviewer';
COMMENT ON COLUMN interviews.declined_at IS 'Timestamp when interview was declined';
COMMENT ON COLUMN interviews.decline_reason IS 'Reason for declining the interview';

COMMENT ON VIEW interview_requests IS 'View of all pending interview requests for interviewers';
COMMENT ON VIEW candidate_interview_history IS 'View of candidate interview history';
COMMENT ON VIEW interviewer_interview_schedule IS 'View of interviewer interview schedule';

-- Sample data for testing (optional - remove in production)
-- INSERT INTO interviews (
--   candidate_id,
--   job_id,
--   company_id,
--   status,
--   preferred_date,
--   duration_minutes,
--   interview_type,
--   message
-- ) VALUES (
--   'sample-candidate-id',
--   'sample-job-id',
--   'sample-company-id',
--   'requested',
--   NOW() + INTERVAL '2 days',
--   60,
--   'video',
--   'I am very excited about this opportunity and would love to discuss my experience.'
-- );
