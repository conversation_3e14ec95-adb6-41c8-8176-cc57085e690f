import React from 'react';
import { Card, Typography, Tag, Button, Space, Avatar } from 'antd';
import {
  VideoCameraOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { INTERVIEW_STATUS } from '@/features/interviewer/constants';

const { Title, Text } = Typography;

const InterviewCard = ({
  interview,
  onAccept,
  onDecline,
  onJoin,
  onViewDetails,
  showActions = true,
}) => {
  const getStatusTag = (status) => {
    switch (status) {
      case INTERVIEW_STATUS.REQUESTED:
        return <Tag color="blue">Requested</Tag>;
      case INTERVIEW_STATUS.ACCEPTED:
        return <Tag color="cyan">Accepted</Tag>;
      case INTERVIEW_STATUS.SCHEDULED:
        return <Tag color="green">Scheduled</Tag>;
      case INTERVIEW_STATUS.COMPLETED:
        return <Tag color="purple">Completed</Tag>;
      case INTERVIEW_STATUS.CANCELLED:
        return <Tag color="red">Cancelled</Tag>;
      case INTERVIEW_STATUS.NO_SHOW:
        return <Tag color="orange">No Show</Tag>;
      default:
        return null;
    }
  };

  const renderActions = () => {
    if (!showActions) return null;

    switch (interview.status) {
      case INTERVIEW_STATUS.REQUESTED:
        return (
          <Space>
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => onAccept(interview)}
            >
              Accept
            </Button>
            <Button
              danger
              icon={<CloseCircleOutlined />}
              onClick={() => onDecline(interview)}
            >
              Decline
            </Button>
          </Space>
        );
      case INTERVIEW_STATUS.SCHEDULED:
        return (
          <Button
            type="primary"
            icon={<VideoCameraOutlined />}
            onClick={() => onJoin(interview)}
          >
            Join Interview
          </Button>
        );
      case INTERVIEW_STATUS.COMPLETED:
        return (
          <Button
            type="default"
            onClick={() => onViewDetails(interview)}
          >
            View Feedback
          </Button>
        );
      default:
        return (
          <Button
            type="default"
            onClick={() => onViewDetails(interview)}
          >
            View Details <RightOutlined />
          </Button>
        );
    }
  };

  return (
    <Card
      hoverable
      className="mb-4 shadow-sm"
    >
      <div className="flex flex-col">
        <div className="flex justify-between items-start">
          <div>
            <Title
              level={4}
              className="mb-1"
            >
              {interview.job_title}
            </Title>
            <Text className="text-lg">{interview.company_name}</Text>
          </div>
          <div>{getStatusTag(interview.status)}</div>
        </div>

        <div className="flex items-center mt-3 mb-2">
          <Avatar
            src={interview.candidate_photo}
            icon={!interview.candidate_photo && <UserOutlined />}
            className="mr-2"
          />
          <Text strong>{interview.candidate_name}</Text>
        </div>

        <Space className="mt-2 mb-3">
          <Tag
            icon={<CalendarOutlined />}
            color="blue"
          >
            {interview.date}
          </Tag>
          <Tag
            icon={<ClockCircleOutlined />}
            color="green"
          >
            {interview.time}
          </Tag>
          <Tag
            icon={<VideoCameraOutlined />}
            color="purple"
          >
            {interview.format}
          </Tag>
        </Space>

        {interview.notes && (
          <Text
            type="secondary"
            className="mb-3"
          >
            {interview.notes}
          </Text>
        )}

        <div className="flex justify-end mt-2">{renderActions()}</div>
      </div>
    </Card>
  );
};

export default InterviewCard;
